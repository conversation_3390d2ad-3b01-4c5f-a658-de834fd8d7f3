TimeQuest Timing Analyzer report for DAC904
Thu Jul 31 20:26:38 2025
Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. TimeQuest Timing Analyzer Summary
  3. Parallel Compilation
  4. SDC File List
  5. Clocks
  6. Slow 1200mV 85C Model Fmax Summary
  7. Timing Closure Recommendations
  8. Slow 1200mV 85C Model Setup Summary
  9. Slow 1200mV 85C Model Hold Summary
 10. Slow 1200mV 85C Model Recovery Summary
 11. Slow 1200mV 85C Model Removal Summary
 12. Slow 1200mV 85C Model Minimum Pulse Width Summary
 13. Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 14. Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 15. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'
 16. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'
 17. Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 18. Setup Times
 19. Hold Times
 20. Clock to Output Times
 21. Minimum Clock to Output Times
 22. Slow 1200mV 85C Model Metastability Report
 23. Slow 1200mV 0C Model Fmax Summary
 24. Slow 1200mV 0C Model Setup Summary
 25. Slow 1200mV 0C Model Hold Summary
 26. Slow 1200mV 0C Model Recovery Summary
 27. Slow 1200mV 0C Model Removal Summary
 28. Slow 1200mV 0C Model Minimum Pulse Width Summary
 29. Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 30. Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 31. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 32. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 33. Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 34. Setup Times
 35. Hold Times
 36. Clock to Output Times
 37. Minimum Clock to Output Times
 38. Slow 1200mV 0C Model Metastability Report
 39. Fast 1200mV 0C Model Setup Summary
 40. Fast 1200mV 0C Model Hold Summary
 41. Fast 1200mV 0C Model Recovery Summary
 42. Fast 1200mV 0C Model Removal Summary
 43. Fast 1200mV 0C Model Minimum Pulse Width Summary
 44. Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 45. Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 46. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 47. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 48. Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 49. Setup Times
 50. Hold Times
 51. Clock to Output Times
 52. Minimum Clock to Output Times
 53. Fast 1200mV 0C Model Metastability Report
 54. Multicorner Timing Analysis Summary
 55. Setup Times
 56. Hold Times
 57. Clock to Output Times
 58. Minimum Clock to Output Times
 59. Board Trace Model Assignments
 60. Input Transition Times
 61. Signal Integrity Metrics (Slow 1200mv 0c Model)
 62. Signal Integrity Metrics (Slow 1200mv 85c Model)
 63. Signal Integrity Metrics (Fast 1200mv 0c Model)
 64. Setup Transfers
 65. Hold Transfers
 66. Report TCCS
 67. Report RSKM
 68. Unconstrained Paths
 69. TimeQuest Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+-------------------------------------------------------------------------+
; TimeQuest Timing Analyzer Summary                                       ;
+--------------------+----------------------------------------------------+
; Quartus II Version ; Version 13.1.0 Build 162 10/23/2013 SJ Web Edition ;
; Revision Name      ; DAC904                                             ;
; Device Family      ; Cyclone IV E                                       ;
; Device Name        ; EP4CE6E22C8                                        ;
; Timing Models      ; Final                                              ;
; Delay Model        ; Combined                                           ;
; Rise/Fall Delays   ; Enabled                                            ;
+--------------------+----------------------------------------------------+


Parallel compilation was disabled, but you have multiple processors available. Enable parallel compilation to reduce compilation time.
+-------------------------------------+
; Parallel Compilation                ;
+----------------------------+--------+
; Processors                 ; Number ;
+----------------------------+--------+
; Number detected on machine ; 20     ;
; Maximum allowed            ; 1      ;
+----------------------------+--------+


+-----------------------------------------------------+
; SDC File List                                       ;
+-----------------+--------+--------------------------+
; SDC File Path   ; Status ; Read at                  ;
+-----------------+--------+--------------------------+
; ../doc/SDC1.sdc ; OK     ; Thu Jul 31 20:26:38 2025 ;
+-----------------+--------+--------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                                                                                                                                                                                                                                                                                             ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clock Name                                            ; Type      ; Period ; Frequency  ; Rise  ; Fall   ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master  ; Source                                                  ; Targets                                                                                                                                                                                 ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; CLK_50M                                               ; Base      ; 20.000 ; 50.0 MHz   ; 0.000 ; 10.000 ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { SYS_CLK }                                                                                                                                                                             ;
; CLK_165M                                              ; Base      ; 6.060  ; 165.02 MHz ; 0.000 ; 3.030  ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { DAC_CLK DAC_DATA[0] DAC_DATA[1] DAC_DATA[2] DAC_DATA[3] DAC_DATA[4] DAC_DATA[5] DAC_DATA[6] DAC_DATA[7] DAC_DATA[8] DAC_DATA[9] DAC_DATA[10] DAC_DATA[11] DAC_DATA[12] DAC_DATA[13] } ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Generated ; 50.000 ; 20.0 MHz   ; 0.000 ; 25.000 ; 50.00      ; 5         ; 2           ;       ;        ;           ;            ; false    ; CLK_50M ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0] ; { u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] }                                                                                                                               ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary                                                          ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 140.69 MHz ; 140.69 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup Summary                                            ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 42.892 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold Summary                                            ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.680 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary                              ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.934  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.715 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                       ; To Node                                         ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 42.892 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.480     ; 6.629      ;
; 43.052 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.494     ; 6.455      ;
; 43.288 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.492     ; 6.221      ;
; 43.504 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.480     ; 6.017      ;
; 43.649 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.494     ; 5.858      ;
; 43.743 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.492     ; 5.766      ;
; 44.187 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.468     ; 5.346      ;
; 44.195 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.473     ; 5.333      ;
; 44.221 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.473     ; 5.307      ;
; 44.335 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.474     ; 5.192      ;
; 44.484 ; key_con:u_key_con|key_delay:u_key1_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.436      ;
; 44.496 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.473     ; 5.032      ;
; 44.518 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.402      ;
; 44.537 ; key_con:u_key_con|key_delay:u_key2_delay|kh[22]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 5.386      ;
; 44.543 ; key_con:u_key_con|key_delay:u_key2_delay|kh[21]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 5.380      ;
; 44.545 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.474     ; 4.982      ;
; 44.581 ; key_con:u_key_con|key_delay:u_key1_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.339      ;
; 44.607 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.474     ; 4.920      ;
; 44.730 ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.190      ;
; 44.741 ; key_con:u_key_con|key_delay:u_key2_delay|kh[20]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 5.182      ;
; 44.803 ; key_con:u_key_con|key_delay:u_key2_delay|kh[19]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 5.120      ;
; 44.835 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.085      ;
; 44.858 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.474     ; 4.669      ;
; 45.021 ; key_con:u_key_con|key_delay:u_key1_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.899      ;
; 45.110 ; key_con:u_key_con|key_delay:u_key1_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.810      ;
; 45.256 ; key_con:u_key_con|key_delay:u_key1_delay|kl[16]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.663      ;
; 45.333 ; key_con:u_key_con|key_delay:u_key1_delay|kh[23]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.581      ;
; 45.370 ; key_con:u_key_con|key_delay:u_key1_delay|kh[28]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.544      ;
; 45.385 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.529      ;
; 45.401 ; key_con:u_key_con|key_delay:u_key2_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 4.514      ;
; 45.403 ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.511      ;
; 45.461 ; key_con:u_key_con|key_delay:u_key1_delay|kh[16]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.453      ;
; 45.464 ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.456      ;
; 45.529 ; key_con:u_key_con|key_delay:u_key3_delay|kl[12]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.391      ;
; 45.556 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.358      ;
; 45.572 ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.348      ;
; 45.591 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.328      ;
; 45.591 ; key_con:u_key_con|key_delay:u_key1_delay|kh[25]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.323      ;
; 45.630 ; key_con:u_key_con|key_delay:u_key3_delay|kl[28]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.290      ;
; 45.635 ; key_con:u_key_con|key_delay:u_key2_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 4.280      ;
; 45.660 ; key_con:u_key_con|key_delay:u_key2_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 4.255      ;
; 45.668 ; key_con:u_key_con|key_delay:u_key3_delay|kl[27]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.252      ;
; 45.713 ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.207      ;
; 45.723 ; key_con:u_key_con|key_delay:u_key1_delay|kh[20]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.191      ;
; 45.728 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 4.187      ;
; 45.730 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.184      ;
; 45.739 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.089     ; 4.173      ;
; 45.788 ; key_con:u_key_con|key_delay:u_key3_delay|kh[13]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.131      ;
; 45.792 ; key_con:u_key_con|key_delay:u_key3_delay|kh[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.127      ;
; 45.812 ; key_con:u_key_con|key_delay:u_key3_delay|kl[29]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.108      ;
; 45.817 ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 4.098      ;
; 45.827 ; key_con:u_key_con|key_delay:u_key1_delay|kh[26]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.087      ;
; 45.842 ; key_con:u_key_con|key_delay:u_key3_delay|kh[28]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.077      ;
; 45.851 ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.068      ;
; 45.868 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.051      ;
; 45.868 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.051      ;
; 45.868 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.051      ;
; 45.879 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.089     ; 4.033      ;
; 45.888 ; key_con:u_key_con|key_delay:u_key3_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.032      ;
; 45.897 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 4.017      ;
; 45.900 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 4.015      ;
; 45.902 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.084     ; 4.015      ;
; 45.904 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[31]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.015      ;
; 45.904 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.015      ;
; 45.904 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.015      ;
; 45.904 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.015      ;
; 45.905 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.015      ;
; 45.932 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 3.983      ;
; 45.932 ; key_con:u_key_con|key_delay:u_key2_delay|kl[16]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.988      ;
; 45.934 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.084     ; 3.983      ;
; 45.936 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[30]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.983      ;
; 45.936 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.983      ;
; 45.936 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.983      ;
; 45.936 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.983      ;
; 45.937 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.983      ;
; 45.959 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 3.956      ;
; 45.962 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 3.953      ;
; 45.964 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.084     ; 3.953      ;
; 45.966 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[31]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.953      ;
; 45.966 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.953      ;
; 45.966 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.953      ;
; 45.966 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.953      ;
; 45.967 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.953      ;
; 45.975 ; key_con:u_key_con|key_delay:u_key3_delay|kl[26]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.945      ;
; 45.981 ; key_con:u_key_con|key_delay:u_key1_delay|kh[24]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 3.933      ;
; 45.984 ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.935      ;
; 46.013 ; key_con:u_key_con|key_delay:u_key2_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 3.902      ;
; 46.021 ; key_con:u_key_con|key_delay:u_key1_delay|kh[22]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.087     ; 3.893      ;
; 46.031 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 3.884      ;
; 46.032 ; key_con:u_key_con|key_delay:u_key3_delay|kh[25]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.887      ;
; 46.033 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.084     ; 3.884      ;
; 46.035 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[30]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.884      ;
; 46.035 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.884      ;
; 46.035 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.884      ;
; 46.035 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.884      ;
; 46.036 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.884      ;
; 46.037 ; key_con:u_key_con|key_delay:u_key1_delay|kh[14]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.089     ; 3.875      ;
; 46.046 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 3.869      ;
; 46.046 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 3.869      ;
; 46.048 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.084     ; 3.869      ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                ;
+-------+-------------------------------------------------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                       ; To Node                                                                                                         ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------------------------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.680 ; add_32bit:u_add_32bit|add[21]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.416      ;
; 0.683 ; add_32bit:u_add_32bit|add[22]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.419      ;
; 0.692 ; add_32bit:u_add_32bit|add[24]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.428      ;
; 0.712 ; add_32bit:u_add_32bit|add[20]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.448      ;
; 0.722 ; add_32bit:u_add_32bit|add[30]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.458      ;
; 0.734 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.028      ;
; 0.734 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.028      ;
; 0.734 ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.028      ;
; 0.734 ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.028      ;
; 0.734 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.028      ;
; 0.735 ; add_32bit:u_add_32bit|add[3]                    ; add_32bit:u_add_32bit|add[3]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.737 ; add_32bit:u_add_32bit|add[18]                   ; add_32bit:u_add_32bit|add[18]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[17]                   ; add_32bit:u_add_32bit|add[17]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[15]                   ; add_32bit:u_add_32bit|add[15]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[12]                   ; add_32bit:u_add_32bit|add[12]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[9]                    ; add_32bit:u_add_32bit|add[9]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[19]                   ; add_32bit:u_add_32bit|add[19]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[16]                   ; add_32bit:u_add_32bit|add[16]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[13]                   ; add_32bit:u_add_32bit|add[13]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[11]                   ; add_32bit:u_add_32bit|add[11]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[8]                    ; add_32bit:u_add_32bit|add[8]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[7]                    ; add_32bit:u_add_32bit|add[7]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[6]                    ; add_32bit:u_add_32bit|add[6]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[5]                    ; add_32bit:u_add_32bit|add[5]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[2]                    ; add_32bit:u_add_32bit|add[2]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[1]                    ; add_32bit:u_add_32bit|add[1]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.739 ; add_32bit:u_add_32bit|add[14]                   ; add_32bit:u_add_32bit|add[14]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; add_32bit:u_add_32bit|add[10]                   ; add_32bit:u_add_32bit|add[10]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; add_32bit:u_add_32bit|add[4]                    ; add_32bit:u_add_32bit|add[4]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.746 ; add_32bit:u_add_32bit|add[27]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.482      ;
; 0.756 ; add_32bit:u_add_32bit|add[0]                    ; add_32bit:u_add_32bit|add[0]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.049      ;
; 0.759 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15] ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.053      ;
; 0.759 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15] ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.053      ;
; 0.759 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15] ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.053      ;
; 0.759 ; key_con:u_key_con|key_delay:u_key3_delay|kh[15] ; key_con:u_key_con|key_delay:u_key3_delay|kh[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.053      ;
; 0.759 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15] ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.053      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19] ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13] ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kh[11] ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[19] ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13] ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[11] ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key3_delay|kl[19] ; key_con:u_key_con|key_delay:u_key3_delay|kl[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key3_delay|kl[13] ; key_con:u_key_con|key_delay:u_key3_delay|kl[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key3_delay|kl[11] ; key_con:u_key_con|key_delay:u_key3_delay|kl[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key3_delay|kh[19] ; key_con:u_key_con|key_delay:u_key3_delay|kh[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key3_delay|kh[13] ; key_con:u_key_con|key_delay:u_key3_delay|kh[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key3_delay|kh[11] ; key_con:u_key_con|key_delay:u_key3_delay|kh[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kh[15] ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.053      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13] ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kl[11] ; key_con:u_key_con|key_delay:u_key2_delay|kl[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29] ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27] ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21] ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[17] ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[29] ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[27] ; key_con:u_key_con|key_delay:u_key1_delay|kl[27]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[21] ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[17] ; key_con:u_key_con|key_delay:u_key1_delay|kl[17]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kl[29] ; key_con:u_key_con|key_delay:u_key3_delay|kl[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kl[27] ; key_con:u_key_con|key_delay:u_key3_delay|kl[27]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kl[21] ; key_con:u_key_con|key_delay:u_key3_delay|kl[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kl[17] ; key_con:u_key_con|key_delay:u_key3_delay|kl[17]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kh[29] ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
+-------+-------------------------------------------------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'                                    ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'                                                                                               ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.954  ; 9.954        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.977  ; 9.977        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.022 ; 10.022       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.046 ; 10.046       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                           ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+------------------------------------------------------------------------------------------------------------------+
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[10]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[11]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[12]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[13]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[14]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[15]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[16]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[17]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[18]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[19]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[20]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[21]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[22]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[23]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[24]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[25]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[26]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[27]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[28]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[31]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[7]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[8]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[9]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[10]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[11]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[12]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[13]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[14]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[16]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[17]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[18]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[19]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[20]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[21]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[22]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[23]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[24]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[25]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[26]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[27]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[28]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[29]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[30]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[31]                                                                  ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]                                                                   ;
; 24.715 ; 24.935       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ;
; 24.722 ; 24.957       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0  ;
; 24.723 ; 24.958       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ;
; 24.723 ; 24.958       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                    ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[16]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[18]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[20]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[22]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[23]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[24]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[25]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[26]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[28]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                  ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                  ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+------------------------------------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 3.422 ; 3.615 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 3.422 ; 3.615 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 2.926 ; 3.147 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 2.776 ; 2.929 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 3.229 ; 3.576 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -1.880 ; -2.110 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -2.268 ; -2.509 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -2.127 ; -2.332 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -1.880 ; -2.110 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -2.508 ; -2.835 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                             ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 3.212  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 37.995 ; 37.925 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 35.720 ; 35.609 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 36.324 ; 36.151 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 36.914 ; 36.635 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 36.428 ; 36.304 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 36.987 ; 36.728 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 37.995 ; 37.925 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 36.428 ; 36.227 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 36.458 ; 36.334 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 36.929 ; 36.684 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 36.808 ; 36.644 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 37.136 ; 36.888 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 35.983 ; 35.911 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 36.210 ; 36.077 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 36.024 ; 35.940 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;        ; 3.178  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 2.715 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 5.394 ; 5.309 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 6.821 ; 6.556 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 7.018 ; 6.791 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 7.669 ; 7.450 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 7.204 ; 7.025 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 7.740 ; 7.539 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 8.943 ; 8.857 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 7.036 ; 6.842 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 7.413 ; 7.240 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 7.370 ; 7.135 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 7.743 ; 7.539 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 7.840 ; 7.630 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 5.984 ; 5.855 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 5.394 ; 5.309 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 5.667 ; 5.557 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 2.682 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


----------------------------------------------
; Slow 1200mV 85C Model Metastability Report ;
----------------------------------------------
No synchronizer chains to report.


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                                                           ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 151.22 MHz ; 151.22 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 43.387 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.632 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.943  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.716 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                 ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                       ; To Node                                         ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 43.387 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.424     ; 6.191      ;
; 43.542 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.437     ; 6.023      ;
; 43.774 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.434     ; 5.794      ;
; 43.978 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.424     ; 5.600      ;
; 44.124 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.437     ; 5.441      ;
; 44.222 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.434     ; 5.346      ;
; 44.646 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.415     ; 4.941      ;
; 44.649 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.421     ; 4.932      ;
; 44.681 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.419     ; 4.902      ;
; 44.695 ; key_con:u_key_con|key_delay:u_key1_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.235      ;
; 44.741 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.189      ;
; 44.768 ; key_con:u_key_con|key_delay:u_key2_delay|kh[22]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 5.163      ;
; 44.780 ; key_con:u_key_con|key_delay:u_key2_delay|kh[21]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 5.151      ;
; 44.791 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.418     ; 4.793      ;
; 44.800 ; key_con:u_key_con|key_delay:u_key1_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.130      ;
; 44.922 ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.008      ;
; 44.930 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.419     ; 4.653      ;
; 44.963 ; key_con:u_key_con|key_delay:u_key2_delay|kh[20]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.968      ;
; 44.992 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.418     ; 4.592      ;
; 45.012 ; key_con:u_key_con|key_delay:u_key2_delay|kh[19]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.919      ;
; 45.040 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.890      ;
; 45.053 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.418     ; 4.531      ;
; 45.218 ; key_con:u_key_con|key_delay:u_key1_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.712      ;
; 45.279 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.418     ; 4.305      ;
; 45.296 ; key_con:u_key_con|key_delay:u_key1_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.634      ;
; 45.454 ; key_con:u_key_con|key_delay:u_key1_delay|kl[16]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 4.475      ;
; 45.563 ; key_con:u_key_con|key_delay:u_key1_delay|kh[23]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.362      ;
; 45.607 ; key_con:u_key_con|key_delay:u_key1_delay|kh[28]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.318      ;
; 45.612 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.313      ;
; 45.637 ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.288      ;
; 45.677 ; key_con:u_key_con|key_delay:u_key1_delay|kh[16]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.248      ;
; 45.680 ; key_con:u_key_con|key_delay:u_key2_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 4.246      ;
; 45.780 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.145      ;
; 45.785 ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.145      ;
; 45.791 ; key_con:u_key_con|key_delay:u_key3_delay|kl[12]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.139      ;
; 45.823 ; key_con:u_key_con|key_delay:u_key1_delay|kh[25]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.102      ;
; 45.846 ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.084      ;
; 45.848 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 4.078      ;
; 45.888 ; key_con:u_key_con|key_delay:u_key2_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 4.038      ;
; 45.932 ; key_con:u_key_con|key_delay:u_key2_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.994      ;
; 45.939 ; key_con:u_key_con|key_delay:u_key1_delay|kh[20]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.986      ;
; 45.942 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 3.980      ;
; 45.944 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.981      ;
; 45.971 ; key_con:u_key_con|key_delay:u_key3_delay|kl[28]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.959      ;
; 45.977 ; key_con:u_key_con|key_delay:u_key3_delay|kh[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.952      ;
; 45.983 ; key_con:u_key_con|key_delay:u_key3_delay|kh[13]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.946      ;
; 46.004 ; key_con:u_key_con|key_delay:u_key3_delay|kl[27]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.926      ;
; 46.016 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.910      ;
; 46.028 ; key_con:u_key_con|key_delay:u_key3_delay|kh[28]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.900      ;
; 46.040 ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.888      ;
; 46.045 ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.885      ;
; 46.053 ; key_con:u_key_con|key_delay:u_key1_delay|kh[26]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.872      ;
; 46.059 ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.867      ;
; 46.069 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 3.853      ;
; 46.105 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.820      ;
; 46.114 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.811      ;
; 46.114 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.811      ;
; 46.114 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.811      ;
; 46.118 ; key_con:u_key_con|key_delay:u_key3_delay|kl[29]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.812      ;
; 46.139 ; key_con:u_key_con|key_delay:u_key3_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.791      ;
; 46.155 ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.773      ;
; 46.191 ; key_con:u_key_con|key_delay:u_key1_delay|kh[24]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.734      ;
; 46.210 ; key_con:u_key_con|key_delay:u_key3_delay|kh[25]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.718      ;
; 46.214 ; key_con:u_key_con|key_delay:u_key1_delay|kh[22]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.711      ;
; 46.217 ; key_con:u_key_con|key_delay:u_key1_delay|kh[14]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 3.705      ;
; 46.219 ; key_con:u_key_con|key_delay:u_key2_delay|kl[16]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.710      ;
; 46.239 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.687      ;
; 46.241 ; key_con:u_key_con|key_delay:u_key1_delay|kl[12]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.689      ;
; 46.244 ; key_con:u_key_con|key_delay:u_key2_delay|kh[26]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.687      ;
; 46.245 ; key_con:u_key_con|key_delay:u_key2_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.681      ;
; 46.246 ; key_con:u_key_con|key_delay:u_key3_delay|kh[8]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.683      ;
; 46.246 ; key_con:u_key_con|key_delay:u_key3_delay|kh[23]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.682      ;
; 46.257 ; key_con:u_key_con|key_delay:u_key3_delay|kl[26]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.673      ;
; 46.277 ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                         ; sel_wave:u_sel_wave|da_out_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.386     ; 3.339      ;
; 46.277 ; key_con:u_key_con|key_delay:u_key3_delay|kh[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.652      ;
; 46.286 ; key_con:u_key_con|key_delay:u_key2_delay|kh[24]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.645      ;
; 46.293 ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.637      ;
; 46.350 ; key_con:u_key_con|key_delay:u_key3_delay|kh[14]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.579      ;
; 46.368 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.557      ;
; 46.369 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[31]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.557      ;
; 46.369 ; key_con:u_key_con|key_delay:u_key3_delay|kh[16]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.559      ;
; 46.370 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 3.557      ;
; 46.370 ; key_con:u_key_con|key_delay:u_key3_delay|kl[11]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.560      ;
; 46.372 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.557      ;
; 46.372 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.557      ;
; 46.373 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.557      ;
; 46.374 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.557      ;
; 46.380 ; key_con:u_key_con|key_delay:u_key3_delay|kh[27]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.548      ;
; 46.393 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.537      ;
; 46.403 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.523      ;
; 46.403 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.523      ;
; 46.403 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.523      ;
; 46.408 ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                         ; sel_wave:u_sel_wave|da_out_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.386     ; 3.208      ;
; 46.415 ; key_con:u_key_con|key_delay:u_key2_delay|kh[25]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.516      ;
; 46.416 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 3.509      ;
; 46.417 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[30]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.076     ; 3.509      ;
; 46.418 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 3.509      ;
; 46.420 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.509      ;
; 46.420 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.509      ;
; 46.421 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.509      ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                 ;
+-------+-------------------------------------------------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                       ; To Node                                                                                                         ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------------------------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.632 ; add_32bit:u_add_32bit|add[21]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.288      ;
; 0.632 ; add_32bit:u_add_32bit|add[22]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.288      ;
; 0.637 ; add_32bit:u_add_32bit|add[24]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.293      ;
; 0.655 ; add_32bit:u_add_32bit|add[20]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.311      ;
; 0.663 ; add_32bit:u_add_32bit|add[30]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.319      ;
; 0.682 ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.951      ;
; 0.682 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.951      ;
; 0.683 ; add_32bit:u_add_32bit|add[3]                    ; add_32bit:u_add_32bit|add[3]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.952      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.684 ; add_32bit:u_add_32bit|add[27]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.340      ;
; 0.684 ; add_32bit:u_add_32bit|add[15]                   ; add_32bit:u_add_32bit|add[15]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.953      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.685 ; add_32bit:u_add_32bit|add[17]                   ; add_32bit:u_add_32bit|add[17]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[13]                   ; add_32bit:u_add_32bit|add[13]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[11]                   ; add_32bit:u_add_32bit|add[11]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[6]                    ; add_32bit:u_add_32bit|add[6]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[5]                    ; add_32bit:u_add_32bit|add[5]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[19]                   ; add_32bit:u_add_32bit|add[19]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[18]                   ; add_32bit:u_add_32bit|add[18]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[12]                   ; add_32bit:u_add_32bit|add[12]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[9]                    ; add_32bit:u_add_32bit|add[9]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[8]                    ; add_32bit:u_add_32bit|add[8]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[7]                    ; add_32bit:u_add_32bit|add[7]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[1]                    ; add_32bit:u_add_32bit|add[1]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.955      ;
; 0.687 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.956      ;
; 0.688 ; add_32bit:u_add_32bit|add[16]                   ; add_32bit:u_add_32bit|add[16]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; add_32bit:u_add_32bit|add[14]                   ; add_32bit:u_add_32bit|add[14]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; add_32bit:u_add_32bit|add[2]                    ; add_32bit:u_add_32bit|add[2]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.957      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[10]                   ; add_32bit:u_add_32bit|add[10]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; add_32bit:u_add_32bit|add[4]                    ; add_32bit:u_add_32bit|add[4]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.703 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15] ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.972      ;
; 0.703 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13] ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29] ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21] ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19] ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[11] ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kl[29] ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kl[21] ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kl[19] ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15] ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13] ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15] ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key3_delay|kl[13] ; key_con:u_key_con|key_delay:u_key3_delay|kl[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key3_delay|kh[15] ; key_con:u_key_con|key_delay:u_key3_delay|kh[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key3_delay|kh[13] ; key_con:u_key_con|key_delay:u_key3_delay|kh[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kh[15] ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kh[13] ; key_con:u_key_con|key_delay:u_key2_delay|kh[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15] ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13] ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.705 ; add_32bit:u_add_32bit|add[22]                   ; add_32bit:u_add_32bit|add[22]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27] ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.974      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kh[17] ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.974      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[27] ; key_con:u_key_con|key_delay:u_key1_delay|kl[27]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.974      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[17] ; key_con:u_key_con|key_delay:u_key1_delay|kl[17]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.974      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[11] ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kl[29] ; key_con:u_key_con|key_delay:u_key3_delay|kl[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kl[21] ; key_con:u_key_con|key_delay:u_key3_delay|kl[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kl[19] ; key_con:u_key_con|key_delay:u_key3_delay|kl[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kl[11] ; key_con:u_key_con|key_delay:u_key3_delay|kl[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kh[29] ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kh[21] ; key_con:u_key_con|key_delay:u_key3_delay|kh[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kh[19] ; key_con:u_key_con|key_delay:u_key3_delay|kh[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kh[11] ; key_con:u_key_con|key_delay:u_key3_delay|kh[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kh[29] ; key_con:u_key_con|key_delay:u_key2_delay|kh[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kh[21] ; key_con:u_key_con|key_delay:u_key2_delay|kh[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kh[19] ; key_con:u_key_con|key_delay:u_key2_delay|kh[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kh[11] ; key_con:u_key_con|key_delay:u_key2_delay|kh[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kl[11] ; key_con:u_key_con|key_delay:u_key2_delay|kl[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.706 ; add_32bit:u_add_32bit|add[0]                    ; add_32bit:u_add_32bit|add[0]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.974      ;
+-------+-------------------------------------------------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.975  ; 9.975        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.991  ; 9.991        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.008 ; 10.008       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.025 ; 10.025       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                          ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------+
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[16] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[17] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[18] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[19] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[20] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[21] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[22] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[23] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[24] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[25] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[26] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[27] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[28] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[29] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kout   ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[10] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[11] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[12] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[13] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[14] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[15] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[7]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[8]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[9]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[10] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[11] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[12] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[13] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[14] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[15] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[16] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[17] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[18] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[19] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[20] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[21] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[22] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[23] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[24] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[25] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[26] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[27] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[28] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[29] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[30] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]  ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[10] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[11] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[12] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[13] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[14] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[15] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[16] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[17] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[18] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[19] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[20] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[21] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[22] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[23] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[24] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[25] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[26] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[27] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[28] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[29] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[30] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[7]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[8]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[9]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kout   ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[10] ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 3.021 ; 3.325 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 3.021 ; 3.325 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 2.579 ; 2.877 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 2.446 ; 2.657 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 2.859 ; 3.287 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -1.655 ; -1.921 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.999 ; -2.307 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -1.872 ; -2.129 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -1.655 ; -1.921 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -2.223 ; -2.618 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                             ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 2.991  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 34.921 ; 34.690 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 32.921 ; 32.693 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 33.470 ; 33.171 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 34.002 ; 33.620 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 33.571 ; 33.306 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 34.077 ; 33.683 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 34.921 ; 34.690 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 33.511 ; 33.241 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 33.556 ; 33.322 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 34.009 ; 33.630 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 33.889 ; 33.591 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 34.184 ; 33.809 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 33.074 ; 32.928 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 33.275 ; 33.078 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 33.036 ; 32.959 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;        ; 2.919  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 2.533 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 5.030 ; 4.848 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 6.338 ; 5.978 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 6.526 ; 6.193 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 7.115 ; 6.790 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 6.701 ; 6.399 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 7.188 ; 6.852 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 8.241 ; 7.974 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 6.504 ; 6.249 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 6.885 ; 6.620 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 6.851 ; 6.489 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 7.176 ; 6.850 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 7.269 ; 6.950 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 5.535 ; 5.346 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 5.030 ; 4.848 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 5.289 ; 5.062 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 2.463 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Slow 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 47.071 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.266 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 2.060  ; 0.000         ;
; CLK_50M                                               ; 9.594  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.733 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                 ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                       ; To Node                                         ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 47.071 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.220     ; 2.696      ;
; 47.140 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.229     ; 2.618      ;
; 47.256 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.226     ; 2.505      ;
; 47.332 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.220     ; 2.435      ;
; 47.345 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.229     ; 2.413      ;
; 47.436 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.226     ; 2.325      ;
; 47.547 ; key_con:u_key_con|key_delay:u_key2_delay|kh[21]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.034     ; 2.406      ;
; 47.568 ; key_con:u_key_con|key_delay:u_key2_delay|kh[22]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.034     ; 2.385      ;
; 47.634 ; key_con:u_key_con|key_delay:u_key2_delay|kh[20]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.034     ; 2.319      ;
; 47.641 ; key_con:u_key_con|key_delay:u_key1_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.311      ;
; 47.641 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.311      ;
; 47.645 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.216     ; 2.126      ;
; 47.653 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.222     ; 2.112      ;
; 47.658 ; key_con:u_key_con|key_delay:u_key2_delay|kh[19]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.034     ; 2.295      ;
; 47.671 ; key_con:u_key_con|key_delay:u_key1_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.281      ;
; 47.674 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.220     ; 2.093      ;
; 47.745 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.219     ; 2.023      ;
; 47.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.192      ;
; 47.802 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.150      ;
; 47.808 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.220     ; 1.959      ;
; 47.831 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.219     ; 1.937      ;
; 47.855 ; key_con:u_key_con|key_delay:u_key1_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.097      ;
; 47.857 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.219     ; 1.911      ;
; 47.917 ; key_con:u_key_con|key_delay:u_key1_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.035      ;
; 47.933 ; key_con:u_key_con|key_delay:u_key1_delay|kh[28]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.018      ;
; 47.937 ; key_con:u_key_con|key_delay:u_key1_delay|kh[23]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.014      ;
; 47.948 ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.003      ;
; 47.965 ; key_con:u_key_con|key_delay:u_key1_delay|kl[16]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.986      ;
; 47.972 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.979      ;
; 47.982 ; key_con:u_key_con|key_delay:u_key2_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.039     ; 1.966      ;
; 47.984 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.219     ; 1.784      ;
; 48.007 ; key_con:u_key_con|key_delay:u_key1_delay|kh[16]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.944      ;
; 48.009 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.942      ;
; 48.018 ; key_con:u_key_con|key_delay:u_key3_delay|kl[12]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.933      ;
; 48.026 ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.925      ;
; 48.043 ; key_con:u_key_con|key_delay:u_key1_delay|kh[25]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.908      ;
; 48.081 ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.870      ;
; 48.091 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.860      ;
; 48.092 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.855      ;
; 48.097 ; key_con:u_key_con|key_delay:u_key1_delay|kh[20]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.854      ;
; 48.100 ; key_con:u_key_con|key_delay:u_key2_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.039     ; 1.848      ;
; 48.107 ; key_con:u_key_con|key_delay:u_key3_delay|kh[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.843      ;
; 48.114 ; key_con:u_key_con|key_delay:u_key2_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.039     ; 1.834      ;
; 48.118 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.039     ; 1.830      ;
; 48.132 ; key_con:u_key_con|key_delay:u_key3_delay|kl[28]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.819      ;
; 48.137 ; key_con:u_key_con|key_delay:u_key1_delay|kh[26]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.814      ;
; 48.140 ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.811      ;
; 48.140 ; key_con:u_key_con|key_delay:u_key3_delay|kl[27]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.811      ;
; 48.146 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.039     ; 1.802      ;
; 48.152 ; key_con:u_key_con|key_delay:u_key3_delay|kh[13]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.798      ;
; 48.169 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[31]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.778      ;
; 48.169 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.778      ;
; 48.171 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.038     ; 1.778      ;
; 48.172 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.778      ;
; 48.173 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[30]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.774      ;
; 48.173 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.774      ;
; 48.173 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.778      ;
; 48.173 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.778      ;
; 48.174 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.778      ;
; 48.175 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.038     ; 1.774      ;
; 48.176 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.774      ;
; 48.177 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.774      ;
; 48.177 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.774      ;
; 48.178 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.774      ;
; 48.181 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.770      ;
; 48.181 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.039     ; 1.767      ;
; 48.182 ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.039     ; 1.766      ;
; 48.183 ; key_con:u_key_con|key_delay:u_key3_delay|kh[28]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.767      ;
; 48.183 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[31]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.764      ;
; 48.183 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.764      ;
; 48.183 ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.767      ;
; 48.185 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.038     ; 1.764      ;
; 48.186 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.764      ;
; 48.187 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.764      ;
; 48.187 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.764      ;
; 48.188 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.764      ;
; 48.196 ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                         ; sel_wave:u_sel_wave|da_out_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.203     ; 1.588      ;
; 48.200 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 1.746      ;
; 48.200 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 1.746      ;
; 48.200 ; key_con:u_key_con|sel[1]                                                                                        ; sel_wave:u_sel_wave|da_out_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 1.746      ;
; 48.203 ; key_con:u_key_con|key_delay:u_key3_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.748      ;
; 48.206 ; key_con:u_key_con|key_delay:u_key3_delay|kl[29]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.745      ;
; 48.212 ; key_con:u_key_con|key_delay:u_key1_delay|kh[24]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.739      ;
; 48.221 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[30]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.726      ;
; 48.221 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.726      ;
; 48.223 ; key_con:u_key_con|key_delay:u_key3_delay|kh[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.727      ;
; 48.223 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.038     ; 1.726      ;
; 48.224 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.726      ;
; 48.225 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.726      ;
; 48.225 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.726      ;
; 48.226 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.726      ;
; 48.233 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.039     ; 1.715      ;
; 48.237 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[29]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.710      ;
; 48.237 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.710      ;
; 48.239 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.038     ; 1.710      ;
; 48.239 ; key_con:u_key_con|key_delay:u_key1_delay|kh[22]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.712      ;
; 48.239 ; add_32bit:u_add_32bit|add[3]                                                                                    ; add_32bit:u_add_32bit|add[31]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.708      ;
; 48.239 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.708      ;
; 48.240 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.710      ;
; 48.240 ; key_con:u_key_con|key_delay:u_key2_delay|kl[16]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.710      ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                 ;
+-------+-------------------------------------------------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                       ; To Node                                                                                                         ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------------------------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.266 ; add_32bit:u_add_32bit|add[22]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.597      ;
; 0.266 ; add_32bit:u_add_32bit|add[24]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.597      ;
; 0.270 ; add_32bit:u_add_32bit|add[20]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.601      ;
; 0.270 ; add_32bit:u_add_32bit|add[21]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.601      ;
; 0.275 ; add_32bit:u_add_32bit|add[30]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.606      ;
; 0.287 ; add_32bit:u_add_32bit|add[27]                   ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.618      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.293 ; add_32bit:u_add_32bit|add[15]                   ; add_32bit:u_add_32bit|add[15]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; add_32bit:u_add_32bit|add[3]                    ; add_32bit:u_add_32bit|add[3]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[19]                   ; add_32bit:u_add_32bit|add[19]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[18]                   ; add_32bit:u_add_32bit|add[18]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[17]                   ; add_32bit:u_add_32bit|add[17]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[16]                   ; add_32bit:u_add_32bit|add[16]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[13]                   ; add_32bit:u_add_32bit|add[13]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[12]                   ; add_32bit:u_add_32bit|add[12]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[11]                   ; add_32bit:u_add_32bit|add[11]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[9]                    ; add_32bit:u_add_32bit|add[9]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[8]                    ; add_32bit:u_add_32bit|add[8]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[7]                    ; add_32bit:u_add_32bit|add[7]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[6]                    ; add_32bit:u_add_32bit|add[6]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[5]                    ; add_32bit:u_add_32bit|add[5]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[1]                    ; add_32bit:u_add_32bit|add[1]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[14]                   ; add_32bit:u_add_32bit|add[14]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[4]                    ; add_32bit:u_add_32bit|add[4]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[2]                    ; add_32bit:u_add_32bit|add[2]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.296 ; add_32bit:u_add_32bit|add[10]                   ; add_32bit:u_add_32bit|add[10]                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.416      ;
; 0.299 ; add_32bit:u_add_32bit|add[0]                    ; add_32bit:u_add_32bit|add[0]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.419      ;
; 0.302 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15] ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.423      ;
; 0.302 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15] ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.423      ;
; 0.302 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15] ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.423      ;
; 0.302 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15] ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.423      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13] ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13] ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; key_con:u_key_con|key_delay:u_key3_delay|kl[31]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key3_delay|kl[13] ; key_con:u_key_con|key_delay:u_key3_delay|kl[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key3_delay|kh[15] ; key_con:u_key_con|key_delay:u_key3_delay|kh[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.423      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key2_delay|kh[15] ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.423      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13] ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29] ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27] ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21] ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19] ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[17] ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[11] ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[7]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[7]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[29] ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[27] ; key_con:u_key_con|key_delay:u_key1_delay|kl[27]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[21] ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[19] ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[17] ; key_con:u_key_con|key_delay:u_key1_delay|kl[17]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[11] ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[29] ; key_con:u_key_con|key_delay:u_key3_delay|kl[29]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[27] ; key_con:u_key_con|key_delay:u_key3_delay|kl[27]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[21] ; key_con:u_key_con|key_delay:u_key3_delay|kl[21]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[19] ; key_con:u_key_con|key_delay:u_key3_delay|kl[19]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[17] ; key_con:u_key_con|key_delay:u_key3_delay|kl[17]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[11] ; key_con:u_key_con|key_delay:u_key3_delay|kl[11]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; key_con:u_key_con|key_delay:u_key3_delay|kh[31]                                                                 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
+-------+-------------------------------------------------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.620  ; 9.620        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.622  ; 9.622        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.378 ; 10.378       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.380 ; 10.380       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                 ;
+--------+--------------+----------------+-----------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type            ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+-----------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0    ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0   ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0   ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0    ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[10]                                                                   ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[11]                                                                   ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[12]                                                                   ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                   ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[14]                                                                   ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                   ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[8]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[9]                                                                    ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.796 ; 24.980       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[10]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[12]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[14]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[16]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[18]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[20]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[22]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[23]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[24]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[25]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[26]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[28]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[7]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[8]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[9]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[16]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[17]                                                                   ;
+--------+--------------+----------------+-----------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 1.716 ; 1.967 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 1.716 ; 1.967 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 1.460 ; 1.764 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 1.390 ; 1.690 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 1.624 ; 1.955 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -0.974 ; -1.315 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.161 ; -1.490 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -1.077 ; -1.416 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -0.974 ; -1.315 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -1.296 ; -1.624 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                             ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 1.526  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 16.377 ; 16.519 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 15.063 ; 15.194 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 15.346 ; 15.431 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 15.581 ; 15.692 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 15.407 ; 15.507 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 15.583 ; 15.687 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 16.377 ; 16.519 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 15.390 ; 15.457 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 15.458 ; 15.547 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 15.604 ; 15.691 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 15.597 ; 15.689 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 15.691 ; 15.788 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 15.257 ; 15.302 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 15.321 ; 15.366 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 15.266 ; 15.289 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;        ; 1.593  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 1.295 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 2.354 ; 2.411 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 2.992 ; 3.043 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 3.063 ; 3.159 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 3.377 ; 3.498 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 3.170 ; 3.275 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 3.380 ; 3.495 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 4.186 ; 4.346 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 3.104 ; 3.182 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 3.270 ; 3.374 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 3.244 ; 3.342 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 3.380 ; 3.490 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 3.451 ; 3.551 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 2.664 ; 2.705 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 2.354 ; 2.411 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 2.452 ; 2.563 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 1.361 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Fast 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                                                                ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Clock                                                  ; Setup  ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Worst-case Slack                                       ; 42.892 ; 0.266 ; N/A      ; N/A     ; 1.616               ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 1.616               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 9.594               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 42.892 ; 0.266 ; N/A      ; N/A     ; 24.715              ;
; Design-wide TNS                                        ; 0.0    ; 0.0   ; 0.0      ; 0.0     ; 0.0                 ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000  ; 0.000 ; N/A      ; N/A     ; 0.000               ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 3.422 ; 3.615 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 3.422 ; 3.615 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 2.926 ; 3.147 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 2.776 ; 2.929 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 3.229 ; 3.576 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -0.974 ; -1.315 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.161 ; -1.490 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -1.077 ; -1.416 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -0.974 ; -1.315 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -1.296 ; -1.624 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                             ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 3.212  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 37.995 ; 37.925 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 35.720 ; 35.609 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 36.324 ; 36.151 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 36.914 ; 36.635 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 36.428 ; 36.304 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 36.987 ; 36.728 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 37.995 ; 37.925 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 36.428 ; 36.227 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 36.458 ; 36.334 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 36.929 ; 36.684 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 36.808 ; 36.644 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 37.136 ; 36.888 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 35.983 ; 35.911 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 36.210 ; 36.077 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 36.024 ; 35.940 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;        ; 3.178  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 1.295 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 2.354 ; 2.411 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 2.992 ; 3.043 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 3.063 ; 3.159 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 3.377 ; 3.498 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 3.170 ; 3.275 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 3.380 ; 3.495 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 4.186 ; 4.346 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 3.104 ; 3.182 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 3.270 ; 3.374 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 3.244 ; 3.342 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 3.380 ; 3.490 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 3.451 ; 3.551 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 2.664 ; 2.705 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 2.354 ; 2.411 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 2.452 ; 2.563 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 1.361 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                    ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin           ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; PD            ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_CLK       ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[4]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[5]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[6]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[7]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[8]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[9]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[10]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[11]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[12]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[13]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; SYS_RST                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; SYS_CLK                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[0]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[2]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[1]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD            ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD            ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD            ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_CLK       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; DAC_DATA[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[8]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[9]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[10]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[11]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[12]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[13]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Setup Transfers                                                                                                                                           ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 4115     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Hold Transfers                                                                                                                                            ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 4115     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths                            ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 2     ; 2    ;
; Unconstrained Input Ports       ; 4     ; 4    ;
; Unconstrained Input Port Paths  ; 272   ; 272  ;
; Unconstrained Output Ports      ; 15    ; 15   ;
; Unconstrained Output Port Paths ; 197   ; 197  ;
+---------------------------------+-------+------+


+------------------------------------+
; TimeQuest Timing Analyzer Messages ;
+------------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit TimeQuest Timing Analyzer
    Info: Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
    Info: Processing started: Thu Jul 31 20:26:37 2025
Info: Command: quartus_sta DAC904 -c DAC904
Info: qsta_default_script.tcl version: #1
Warning (20028): Parallel compilation is not licensed and has been disabled
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Info (332104): Reading SDC File: '../doc/SDC1.sdc'
Info (332110): Deriving PLL clocks
    Info (332110): create_generated_clock -source {u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]} {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]}
Info (332151): Clock uncertainty is not calculated until you update the timing netlist.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key2_delay|kout was determined to be a clock but was found without an associated clock assignment.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key1_delay|kout was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info: Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Info (332146): Worst-case setup slack is 42.892
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    42.892               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.680
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.680               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.934               0.000 CLK_50M 
    Info (332119):    24.715               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Warning (332060): Node: key_con:u_key_con|key_delay:u_key2_delay|kout was determined to be a clock but was found without an associated clock assignment.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key1_delay|kout was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 43.387
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    43.387               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.632
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.632               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.943               0.000 CLK_50M 
    Info (332119):    24.716               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Fast 1200mV 0C Model
Warning (332060): Node: key_con:u_key_con|key_delay:u_key2_delay|kout was determined to be a clock but was found without an associated clock assignment.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key1_delay|kout was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 47.071
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    47.071               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.266
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.266               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 2.060
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     2.060               0.000 CLK_165M 
    Info (332119):     9.594               0.000 CLK_50M 
    Info (332119):    24.733               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 7 warnings
    Info: Peak virtual memory: 4641 megabytes
    Info: Processing ended: Thu Jul 31 20:26:38 2025
    Info: Elapsed time: 00:00:01
    Info: Total CPU time (on all processors): 00:00:01


