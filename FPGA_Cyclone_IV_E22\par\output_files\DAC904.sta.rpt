TimeQuest Timing Analyzer report for DAC904
Thu Jul 31 20:41:13 2025
Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. TimeQuest Timing Analyzer Summary
  3. Parallel Compilation
  4. SDC File List
  5. Clocks
  6. Slow 1200mV 85C Model Fmax Summary
  7. Timing Closure Recommendations
  8. Slow 1200mV 85C Model Setup Summary
  9. Slow 1200mV 85C Model Hold Summary
 10. Slow 1200mV 85C Model Recovery Summary
 11. Slow 1200mV 85C Model Removal Summary
 12. Slow 1200mV 85C Model Minimum Pulse Width Summary
 13. Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 14. Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 15. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'
 16. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'
 17. Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 18. Setup Times
 19. Hold Times
 20. Clock to Output Times
 21. Minimum Clock to Output Times
 22. Slow 1200mV 85C Model Metastability Report
 23. Slow 1200mV 0C Model Fmax Summary
 24. Slow 1200mV 0C Model Setup Summary
 25. Slow 1200mV 0C Model Hold Summary
 26. Slow 1200mV 0C Model Recovery Summary
 27. Slow 1200mV 0C Model Removal Summary
 28. Slow 1200mV 0C Model Minimum Pulse Width Summary
 29. Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 30. Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 31. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 32. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 33. Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 34. Setup Times
 35. Hold Times
 36. Clock to Output Times
 37. Minimum Clock to Output Times
 38. Slow 1200mV 0C Model Metastability Report
 39. Fast 1200mV 0C Model Setup Summary
 40. Fast 1200mV 0C Model Hold Summary
 41. Fast 1200mV 0C Model Recovery Summary
 42. Fast 1200mV 0C Model Removal Summary
 43. Fast 1200mV 0C Model Minimum Pulse Width Summary
 44. Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 45. Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 46. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 47. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 48. Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 49. Setup Times
 50. Hold Times
 51. Clock to Output Times
 52. Minimum Clock to Output Times
 53. Fast 1200mV 0C Model Metastability Report
 54. Multicorner Timing Analysis Summary
 55. Setup Times
 56. Hold Times
 57. Clock to Output Times
 58. Minimum Clock to Output Times
 59. Board Trace Model Assignments
 60. Input Transition Times
 61. Signal Integrity Metrics (Slow 1200mv 0c Model)
 62. Signal Integrity Metrics (Slow 1200mv 85c Model)
 63. Signal Integrity Metrics (Fast 1200mv 0c Model)
 64. Setup Transfers
 65. Hold Transfers
 66. Report TCCS
 67. Report RSKM
 68. Unconstrained Paths
 69. TimeQuest Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+-------------------------------------------------------------------------+
; TimeQuest Timing Analyzer Summary                                       ;
+--------------------+----------------------------------------------------+
; Quartus II Version ; Version 13.1.0 Build 162 10/23/2013 SJ Web Edition ;
; Revision Name      ; DAC904                                             ;
; Device Family      ; Cyclone IV E                                       ;
; Device Name        ; EP4CE6E22C8                                        ;
; Timing Models      ; Final                                              ;
; Delay Model        ; Combined                                           ;
; Rise/Fall Delays   ; Enabled                                            ;
+--------------------+----------------------------------------------------+


Parallel compilation was disabled, but you have multiple processors available. Enable parallel compilation to reduce compilation time.
+-------------------------------------+
; Parallel Compilation                ;
+----------------------------+--------+
; Processors                 ; Number ;
+----------------------------+--------+
; Number detected on machine ; 20     ;
; Maximum allowed            ; 1      ;
+----------------------------+--------+


+-----------------------------------------------------+
; SDC File List                                       ;
+-----------------+--------+--------------------------+
; SDC File Path   ; Status ; Read at                  ;
+-----------------+--------+--------------------------+
; ../doc/SDC1.sdc ; OK     ; Thu Jul 31 20:41:12 2025 ;
+-----------------+--------+--------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                                                                                                                                                                                                                                                                                             ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clock Name                                            ; Type      ; Period ; Frequency  ; Rise  ; Fall   ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master  ; Source                                                  ; Targets                                                                                                                                                                                 ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; CLK_50M                                               ; Base      ; 20.000 ; 50.0 MHz   ; 0.000 ; 10.000 ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { SYS_CLK }                                                                                                                                                                             ;
; CLK_165M                                              ; Base      ; 6.060  ; 165.02 MHz ; 0.000 ; 3.030  ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { DAC_CLK DAC_DATA[0] DAC_DATA[1] DAC_DATA[2] DAC_DATA[3] DAC_DATA[4] DAC_DATA[5] DAC_DATA[6] DAC_DATA[7] DAC_DATA[8] DAC_DATA[9] DAC_DATA[10] DAC_DATA[11] DAC_DATA[12] DAC_DATA[13] } ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Generated ; 50.000 ; 20.0 MHz   ; 0.000 ; 25.000 ; 50.00      ; 5         ; 2           ;       ;        ;           ;            ; false    ; CLK_50M ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0] ; { u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] }                                                                                                                               ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary                                                          ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 105.57 MHz ; 105.57 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup Summary                                            ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 40.528 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold Summary                                            ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.453 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary                              ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.934  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.718 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                                      ;
+--------+-----------------------------------------------------------------------------------------------------------------+-----------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                       ; To Node                                                               ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+-----------------------------------------------------------------------------------------------------------------+-----------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 40.528 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 9.377      ;
; 40.747 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 9.176      ;
; 40.843 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 9.062      ;
; 40.863 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 9.042      ;
; 41.047 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.858      ;
; 41.048 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.857      ;
; 41.121 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.784      ;
; 41.173 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.732      ;
; 41.212 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.693      ;
; 41.215 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.690      ;
; 41.390 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 8.533      ;
; 41.429 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.476      ;
; 41.455 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 8.468      ;
; 41.485 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.420      ;
; 41.516 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.389      ;
; 41.527 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.378      ;
; 41.551 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.354      ;
; 41.580 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.325      ;
; 41.597 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.308      ;
; 41.673 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.232      ;
; 41.786 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[1]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.119      ;
; 41.821 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 8.102      ;
; 41.831 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.074      ;
; 41.851 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 8.054      ;
; 42.035 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.870      ;
; 42.036 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.869      ;
; 42.109 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.796      ;
; 42.275 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[1]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.630      ;
; 42.318 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.587      ;
; 42.381 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 7.545      ;
; 42.529 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                              ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 7.394      ;
; 42.534 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.371      ;
; 42.536 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[14]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.093     ; 7.372      ;
; 42.622 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 7.301      ;
; 42.625 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[30]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.102     ; 7.274      ;
; 42.632 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[1]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.273      ;
; 42.644 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.261      ;
; 42.654 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.251      ;
; 42.674 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[0]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.231      ;
; 42.729 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.176      ;
; 42.772 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.133      ;
; 42.779 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 7.141      ;
; 42.825 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[22]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.102     ; 7.074      ;
; 42.828 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.077      ;
; 42.843 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.062      ;
; 42.870 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.035      ;
; 42.871 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.034      ;
; 42.891 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[27]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.102     ; 7.008      ;
; 42.894 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 7.011      ;
; 42.902 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 7.021      ;
; 42.921 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.984      ;
; 42.941 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.964      ;
; 42.965 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.940      ;
; 42.996 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.924      ;
; 43.011 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.894      ;
; 43.022 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[13]                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.488     ; 6.491      ;
; 43.025 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 6.898      ;
; 43.044 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[12]                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.488     ; 6.469      ;
; 43.051 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.854      ;
; 43.052 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.868      ;
; 43.074 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.846      ;
; 43.086 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[19]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.102     ; 6.813      ;
; 43.087 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.818      ;
; 43.090 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[9]                                          ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.093     ; 6.818      ;
; 43.094 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.826      ;
; 43.103 ; sel_wave:u_sel_wave|da_out_reg[10]                                                                              ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.802      ;
; 43.118 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.802      ;
; 43.120 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[9]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.489     ; 6.392      ;
; 43.129 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[8]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.489     ; 6.383      ;
; 43.129 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[29]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.102     ; 6.770      ;
; 43.131 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[10]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.093     ; 6.777      ;
; 43.142 ; sel_wave:u_sel_wave|da_out_reg[13]                                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.763      ;
; 43.142 ; sel_wave:u_sel_wave|da_out_reg[13]                                                                              ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.763      ;
; 43.144 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[7]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.776      ;
; 43.160 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 6.766      ;
; 43.163 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[0]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.742      ;
; 43.164 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.756      ;
; 43.182 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[7]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.486     ; 6.333      ;
; 43.182 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[3]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.487     ; 6.332      ;
; 43.184 ; sel_wave:u_sel_wave|da_out_reg[9]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.721      ;
; 43.186 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[6]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.486     ; 6.329      ;
; 43.192 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.093     ; 6.716      ;
; 43.230 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[2]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.488     ; 6.283      ;
; 43.235 ; sel_wave:u_sel_wave|da_out_reg[9]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.670      ;
; 43.240 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.680      ;
; 43.252 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[28]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.102     ; 6.647      ;
; 43.255 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[14]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.102     ; 6.644      ;
; 43.256 ; sel_wave:u_sel_wave|da_out_reg[10]                                                                              ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.664      ;
; 43.268 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.637      ;
; 43.271 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[7]                                          ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.093     ; 6.637      ;
; 43.280 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[21]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.102     ; 6.619      ;
; 43.291 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[22]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.093     ; 6.617      ;
; 43.293 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                              ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 6.633      ;
; 43.324 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.581      ;
; 43.332 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 6.591      ;
; 43.346 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.559      ;
; 43.351 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[25]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.093     ; 6.557      ;
; 43.359 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.561      ;
; 43.366 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.096     ; 6.539      ;
; 43.375 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 6.548      ;
+--------+-----------------------------------------------------------------------------------------------------------------+-----------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                                         ;
+-------+-------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                                               ; To Node                                                                                                          ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.453 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                      ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                      ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.746      ;
; 0.465 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                      ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.758      ;
; 0.498 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout   ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.791      ;
; 0.526 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                      ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.819      ;
; 0.555 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                      ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.848      ;
; 0.650 ; add_32bit:u_add_32bit|add[26]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.384      ;
; 0.684 ; add_32bit:u_add_32bit|add[22]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.418      ;
; 0.686 ; add_32bit:u_add_32bit|add[30]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.420      ;
; 0.703 ; add_32bit:u_add_32bit|add[21]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.437      ;
; 0.703 ; add_32bit:u_add_32bit|add[31]                                           ; add_32bit:u_add_32bit|add[31]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.996      ;
; 0.718 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[17] ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.083      ; 1.013      ;
; 0.721 ; add_32bit:u_add_32bit|add[31]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.455      ;
; 0.731 ; add_32bit:u_add_32bit|add[20]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.465      ;
; 0.734 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[3]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[3]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.028      ;
; 0.735 ; add_32bit:u_add_32bit|add[3]                                            ; add_32bit:u_add_32bit|add[3]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[3]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[3]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[5]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[5]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[1]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[1]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[5]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[5]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[1]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[1]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.737 ; add_32bit:u_add_32bit|add[18]                                           ; add_32bit:u_add_32bit|add[18]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[17]                                           ; add_32bit:u_add_32bit|add[17]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[15]                                           ; add_32bit:u_add_32bit|add[15]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[12]                                           ; add_32bit:u_add_32bit|add[12]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[9]                                            ; add_32bit:u_add_32bit|add[9]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[6]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[6]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[2]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[2]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[19]                                           ; add_32bit:u_add_32bit|add[19]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[16]                                           ; add_32bit:u_add_32bit|add[16]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[11]                                           ; add_32bit:u_add_32bit|add[11]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[8]                                            ; add_32bit:u_add_32bit|add[8]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[7]                                            ; add_32bit:u_add_32bit|add[7]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[6]                                            ; add_32bit:u_add_32bit|add[6]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[5]                                            ; add_32bit:u_add_32bit|add[5]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[2]                                            ; add_32bit:u_add_32bit|add[2]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[1]                                            ; add_32bit:u_add_32bit|add[1]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[6]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[6]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[2]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[2]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[4]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[4]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.739 ; add_32bit:u_add_32bit|add[14]                                           ; add_32bit:u_add_32bit|add[14]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; add_32bit:u_add_32bit|add[10]                                           ; add_32bit:u_add_32bit|add[10]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; add_32bit:u_add_32bit|add[4]                                            ; add_32bit:u_add_32bit|add[4]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[4]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[4]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.744 ; key_con:u_key_con|sel[1]                                                ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.038      ;
; 0.744 ; key_con:u_key_con|sel[1]                                                ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.038      ;
; 0.756 ; add_32bit:u_add_32bit|add[0]                                            ; add_32bit:u_add_32bit|add[0]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.049      ;
; 0.758 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout   ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.051      ;
; 0.759 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[15] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[15]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.053      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.053      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.053      ;
; 0.760 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[19] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[19]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.053      ;
; 0.760 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[19] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[19]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[13] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[13]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[11] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[11]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[0]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[0]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                         ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.053      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                         ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.053      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[27]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[17]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[29] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[29]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
+-------+-------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'                                    ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'                                                                                               ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.954  ; 9.954        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.977  ; 9.977        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.022 ; 10.022       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.046 ; 10.046       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                           ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+------------------------------------------------------------------------------------------------------------------+
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[10]                                                                  ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                  ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[12]                                                                  ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                  ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[14]                                                                  ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                  ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[8]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[9]                                                                   ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kout                                                                    ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                   ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[10]                                                                  ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                  ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[12]                                                                  ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                  ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[14]                                                                  ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                  ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                   ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                   ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                   ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                   ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                   ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                   ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[7]                                                                   ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[8]                                                                   ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[9]                                                                   ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[16]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[17]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[18]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[19]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[20]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[21]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[22]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[23]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[24]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[25]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[26]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[27]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[28]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[29]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[30]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[31]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[16]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[17]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[18]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[19]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[20]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[21]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[22]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[23]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[24]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[25]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[26]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[27]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[28]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[29]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[30]                                                                  ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[31]                                                                  ;
; 24.722 ; 24.957       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0 ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[0]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[10]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[11]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[12]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[13]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[14]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[16]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[17]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[18]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[19]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[1]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[20]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[21]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[22]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[23]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[24]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[25]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[26]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[27]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[28]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[29]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[2]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[30]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[31]                                          ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[3]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[4]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[5]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[6]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[7]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[8]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[9]                                           ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ;
; 24.723 ; 24.958       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                          ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+------------------------------------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 3.383 ; 3.577 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 3.383 ; 3.577 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 2.901 ; 3.102 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 2.972 ; 3.196 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 2.917 ; 3.224 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -2.025 ; -2.133 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -2.050 ; -2.221 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -2.061 ; -2.326 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -2.025 ; -2.133 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -1.953 ; -2.185 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                           ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 3.199 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 6.064 ; 6.110 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 4.926 ; 4.841 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 4.925 ; 4.844 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 4.970 ; 4.891 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 4.828 ; 4.733 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 4.917 ; 4.830 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 6.064 ; 6.110 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 4.191 ; 4.159 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 4.671 ; 4.637 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 4.286 ; 4.266 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 4.675 ; 4.636 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 4.725 ; 4.688 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 4.676 ; 4.637 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 4.726 ; 4.666 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 4.566 ; 4.624 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 3.157 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 2.703 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 3.639 ; 3.607 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 4.345 ; 4.261 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 4.344 ; 4.265 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 4.387 ; 4.310 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 4.251 ; 4.158 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 4.336 ; 4.251 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 5.495 ; 5.543 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 3.639 ; 3.607 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 4.104 ; 4.071 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 3.734 ; 3.714 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 4.108 ; 4.070 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 4.156 ; 4.120 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 4.109 ; 4.070 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 4.156 ; 4.098 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 3.999 ; 4.055 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 2.662 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


----------------------------------------------
; Slow 1200mV 85C Model Metastability Report ;
----------------------------------------------
No synchronizer chains to report.


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                                                           ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 111.27 MHz ; 111.27 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 41.013 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.402 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.943  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.716 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                                       ;
+--------+-----------------------------------------------------------------------------------------------------------------+-----------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                       ; To Node                                                               ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+-----------------------------------------------------------------------------------------------------------------+-----------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 41.013 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 8.904      ;
; 41.179 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 8.750      ;
; 41.326 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 8.591      ;
; 41.464 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 8.452      ;
; 41.509 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 8.407      ;
; 41.584 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 8.333      ;
; 41.624 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 8.293      ;
; 41.653 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 8.264      ;
; 41.663 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 8.254      ;
; 41.728 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 8.189      ;
; 41.768 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 8.161      ;
; 41.877 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 8.039      ;
; 41.930 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 7.986      ;
; 41.938 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 7.991      ;
; 41.946 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.971      ;
; 41.963 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.954      ;
; 41.966 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.951      ;
; 42.031 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.886      ;
; 42.085 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.832      ;
; 42.101 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.816      ;
; 42.201 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 7.728      ;
; 42.227 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[1]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.690      ;
; 42.259 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.658      ;
; 42.397 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 7.519      ;
; 42.442 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 7.474      ;
; 42.517 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.400      ;
; 42.557 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.360      ;
; 42.680 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.237      ;
; 42.695 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[1]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 7.222      ;
; 42.737 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 7.195      ;
; 42.948 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[14]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 6.974      ;
; 42.959 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[30]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.955      ;
; 42.960 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                              ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 6.969      ;
; 42.991 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[1]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 6.925      ;
; 42.992 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.925      ;
; 43.008 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[0]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.909      ;
; 43.012 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 6.917      ;
; 43.013 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.904      ;
; 43.038 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.879      ;
; 43.139 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 6.777      ;
; 43.162 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[22]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.752      ;
; 43.191 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 6.739      ;
; 43.192 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 6.724      ;
; 43.217 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[27]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.697      ;
; 43.228 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.689      ;
; 43.259 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 6.657      ;
; 43.271 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 6.645      ;
; 43.313 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.604      ;
; 43.331 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 6.598      ;
; 43.348 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.569      ;
; 43.353 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 6.576      ;
; 43.363 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.554      ;
; 43.378 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.539      ;
; 43.406 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[19]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.508      ;
; 43.413 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.504      ;
; 43.415 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 6.514      ;
; 43.424 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[9]                                          ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 6.498      ;
; 43.427 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.490      ;
; 43.445 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[29]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.469      ;
; 43.463 ; sel_wave:u_sel_wave|da_out_reg[10]                                                                              ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.454      ;
; 43.467 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.450      ;
; 43.468 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 6.461      ;
; 43.468 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[10]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 6.454      ;
; 43.469 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 6.461      ;
; 43.476 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[0]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.441      ;
; 43.481 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.436      ;
; 43.487 ; sel_wave:u_sel_wave|da_out_reg[9]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.430      ;
; 43.504 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 6.426      ;
; 43.517 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[7]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 6.413      ;
; 43.521 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[13]                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.432     ; 6.049      ;
; 43.543 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[12]                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.432     ; 6.027      ;
; 43.549 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[14]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.365      ;
; 43.551 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 6.371      ;
; 43.554 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[28]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.360      ;
; 43.564 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 6.368      ;
; 43.569 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 6.361      ;
; 43.586 ; sel_wave:u_sel_wave|da_out_reg[13]                                                                              ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.331      ;
; 43.588 ; sel_wave:u_sel_wave|da_out_reg[13]                                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.329      ;
; 43.588 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[21]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.326      ;
; 43.595 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[7]                                          ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 6.327      ;
; 43.612 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                              ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 6.320      ;
; 43.618 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[9]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.432     ; 5.952      ;
; 43.619 ; sel_wave:u_sel_wave|da_out_reg[10]                                                                              ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 6.311      ;
; 43.623 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 6.307      ;
; 43.629 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[8]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.432     ; 5.941      ;
; 43.639 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 6.291      ;
; 43.642 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[22]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 6.280      ;
; 43.642 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 6.287      ;
; 43.651 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 6.265      ;
; 43.678 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[7]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.428     ; 5.896      ;
; 43.680 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 6.249      ;
; 43.682 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[3]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.431     ; 5.889      ;
; 43.686 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[6]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.428     ; 5.888      ;
; 43.702 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[25]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 6.220      ;
; 43.704 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.086     ; 6.212      ;
; 43.705 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.212      ;
; 43.707 ; sel_wave:u_sel_wave|da_out_reg[9]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.085     ; 6.210      ;
; 43.714 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[20]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.200      ;
; 43.727 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[16]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.088     ; 6.187      ;
; 43.730 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[2]                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.432     ; 5.840      ;
+--------+-----------------------------------------------------------------------------------------------------------------+-----------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                                          ;
+-------+-------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                                               ; To Node                                                                                                          ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.402 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                      ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                      ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.669      ;
; 0.417 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                      ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.684      ;
; 0.463 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout   ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.730      ;
; 0.484 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                      ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.751      ;
; 0.517 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                      ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.784      ;
; 0.604 ; add_32bit:u_add_32bit|add[26]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.260      ;
; 0.633 ; add_32bit:u_add_32bit|add[31]                                           ; add_32bit:u_add_32bit|add[31]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.900      ;
; 0.635 ; add_32bit:u_add_32bit|add[22]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.291      ;
; 0.637 ; add_32bit:u_add_32bit|add[30]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.293      ;
; 0.642 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[17] ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.911      ;
; 0.645 ; add_32bit:u_add_32bit|add[21]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.301      ;
; 0.658 ; key_con:u_key_con|sel[1]                                                ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.926      ;
; 0.658 ; key_con:u_key_con|sel[1]                                                ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.926      ;
; 0.664 ; add_32bit:u_add_32bit|add[31]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.320      ;
; 0.675 ; add_32bit:u_add_32bit|add[20]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.331      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[5]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[5]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[3]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[3]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[5]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[5]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[3]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[3]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.684 ; add_32bit:u_add_32bit|add[3]                                            ; add_32bit:u_add_32bit|add[3]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.951      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[1]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[1]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[1]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[1]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.685 ; add_32bit:u_add_32bit|add[15]                                           ; add_32bit:u_add_32bit|add[15]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.952      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[6]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[6]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[6]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[6]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[17]                                           ; add_32bit:u_add_32bit|add[17]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[11]                                           ; add_32bit:u_add_32bit|add[11]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[6]                                            ; add_32bit:u_add_32bit|add[6]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[5]                                            ; add_32bit:u_add_32bit|add[5]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.687 ; add_32bit:u_add_32bit|add[19]                                           ; add_32bit:u_add_32bit|add[19]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[18]                                           ; add_32bit:u_add_32bit|add[18]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[12]                                           ; add_32bit:u_add_32bit|add[12]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[9]                                            ; add_32bit:u_add_32bit|add[9]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[8]                                            ; add_32bit:u_add_32bit|add[8]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[7]                                            ; add_32bit:u_add_32bit|add[7]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.688 ; add_32bit:u_add_32bit|add[1]                                            ; add_32bit:u_add_32bit|add[1]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.955      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[2]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[2]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[2]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[2]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[16]                                           ; add_32bit:u_add_32bit|add[16]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[14]                                           ; add_32bit:u_add_32bit|add[14]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[2]                                            ; add_32bit:u_add_32bit|add[2]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[4]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[4]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[4]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[4]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.690 ; add_32bit:u_add_32bit|add[10]                                           ; add_32bit:u_add_32bit|add[10]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.957      ;
; 0.690 ; add_32bit:u_add_32bit|add[4]                                            ; add_32bit:u_add_32bit|add[4]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.957      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[13] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[13]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[15] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[15]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[13] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[13]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                         ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                         ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                         ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kh[13]                         ; key_con:u_key_con|key_delay:u_key2_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.974      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.974      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[29] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[29]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[21] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[21]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[19] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[19]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[11] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[11]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[29] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[29]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[21] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[21]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[19] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[19]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[11] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[11]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
+-------+-------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.975  ; 9.975        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.991  ; 9.991        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.008 ; 10.008       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.025 ; 10.025       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                        ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                  ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------+
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                          ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[10]                         ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                         ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[12]                         ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                         ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[14]                         ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                         ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                          ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                          ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                          ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                          ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                          ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                          ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[7]                          ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[8]                          ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[9]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[10]                         ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                         ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[12]                         ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                         ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[14]                         ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                         ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[8]                          ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[9]                          ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[0]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[10] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[11] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[12] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[13] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[14] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[16] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[17] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[18] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[19] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[1]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[20] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[21] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[22] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[23] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[24] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[25] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[26] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[27] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[28] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[29] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[2]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[30] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[31] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[3]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[4]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[5]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[6]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[7]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[8]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[9]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kout                           ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[16]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[18]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[20]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[22]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[23]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[24]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[25]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[26]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[28]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[16]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[17]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[18]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[19]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[20]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[21]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[22]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[23]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[24]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[25]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[26]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[27]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[28]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[29]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[30]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[31]                         ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kout                           ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[3]                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                       ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 2.994 ; 3.288 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 2.994 ; 3.288 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 2.549 ; 2.836 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 2.646 ; 2.891 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 2.583 ; 2.938 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -1.778 ; -1.933 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.811 ; -2.017 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -1.815 ; -2.131 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -1.778 ; -1.933 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -1.726 ; -1.988 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                           ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 2.980 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 5.557 ; 5.521 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 4.589 ; 4.450 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 4.589 ; 4.454 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 4.627 ; 4.494 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 4.509 ; 4.350 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 4.580 ; 4.441 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 5.557 ; 5.521 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 3.882 ; 3.837 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 4.360 ; 4.256 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 3.989 ; 3.924 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 4.363 ; 4.255 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 4.414 ; 4.302 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 4.364 ; 4.255 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 4.414 ; 4.281 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 4.202 ; 4.299 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 2.903 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 2.523 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 3.374 ; 3.330 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 4.053 ; 3.918 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 4.053 ; 3.922 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 4.089 ; 3.960 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 3.976 ; 3.823 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 4.044 ; 3.910 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 5.033 ; 5.000 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 3.374 ; 3.330 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 3.838 ; 3.736 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 3.481 ; 3.417 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 3.841 ; 3.735 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 3.890 ; 3.780 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 3.841 ; 3.735 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 3.890 ; 3.760 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 3.681 ; 3.775 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 2.447 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Slow 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 45.958 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.187 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 2.060  ; 0.000         ;
; CLK_50M                                               ; 9.594  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.734 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                                       ;
+--------+-----------------------------------------------------------------------------------------------------------------+-----------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                       ; To Node                                                               ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+-----------------------------------------------------------------------------------------------------------------+-----------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 45.958 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.986      ;
; 45.962 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.989      ;
; 46.008 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.936      ;
; 46.095 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.849      ;
; 46.108 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.836      ;
; 46.114 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.830      ;
; 46.123 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.821      ;
; 46.152 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.792      ;
; 46.154 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.790      ;
; 46.189 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.755      ;
; 46.232 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.719      ;
; 46.264 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.680      ;
; 46.304 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.640      ;
; 46.319 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.625      ;
; 46.322 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.622      ;
; 46.334 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.617      ;
; 46.351 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.593      ;
; 46.370 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.574      ;
; 46.373 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.571      ;
; 46.377 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.574      ;
; 46.423 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.521      ;
; 46.445 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.499      ;
; 46.450 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[1]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.494      ;
; 46.510 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.434      ;
; 46.523 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.421      ;
; 46.529 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.415      ;
; 46.538 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.406      ;
; 46.570 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[14]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 3.382      ;
; 46.610 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 3.340      ;
; 46.655 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.289      ;
; 46.655 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.289      ;
; 46.705 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[1]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.239      ;
; 46.721 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.223      ;
; 46.734 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.217      ;
; 46.745 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[0]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.199      ;
; 46.764 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                              ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.187      ;
; 46.771 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.173      ;
; 46.776 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[1]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.168      ;
; 46.782 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.162      ;
; 46.796 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[30]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 3.146      ;
; 46.800 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.151      ;
; 46.803 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.141      ;
; 46.819 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.125      ;
; 46.843 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.101      ;
; 46.848 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[22]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 3.094      ;
; 46.858 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.086      ;
; 46.869 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.075      ;
; 46.883 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.068      ;
; 46.884 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.060      ;
; 46.897 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[27]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 3.045      ;
; 46.899 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[3]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.052      ;
; 46.909 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.035      ;
; 46.910 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.034      ;
; 46.910 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.041      ;
; 46.916 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.028      ;
; 46.931 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.013      ;
; 46.946 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 3.006      ;
; 46.950 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 3.001      ;
; 46.965 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.986      ;
; 46.966 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[19]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 2.976      ;
; 46.975 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.969      ;
; 46.980 ; sel_wave:u_sel_wave|da_out_reg[13]                                                                              ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.964      ;
; 46.982 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[10]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.970      ;
; 46.990 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[29]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 2.952      ;
; 46.995 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[9]                                          ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.957      ;
; 46.997 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.954      ;
; 46.999 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[0]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.945      ;
; 47.010 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.934      ;
; 47.010 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                              ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 2.940      ;
; 47.011 ; sel_wave:u_sel_wave|da_out_reg[9]                                                                               ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.933      ;
; 47.011 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 2.939      ;
; 47.016 ; sel_wave:u_sel_wave|da_out_reg[4]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.935      ;
; 47.020 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.924      ;
; 47.021 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[22]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.931      ;
; 47.023 ; sel_wave:u_sel_wave|da_out_reg[13]                                                                              ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.921      ;
; 47.043 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[2]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.908      ;
; 47.043 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[25]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.909      ;
; 47.044 ; sel_wave:u_sel_wave|da_out_reg[9]                                                                               ; amplitude_control:u_amplitude_control|data_out[5]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.900      ;
; 47.050 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[14]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 2.892      ;
; 47.050 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[21]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 2.892      ;
; 47.051 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[7]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.900      ;
; 47.054 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.897      ;
; 47.055 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[28]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 2.887      ;
; 47.056 ; sel_wave:u_sel_wave|da_out_reg[8]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.895      ;
; 47.060 ; sel_wave:u_sel_wave|da_out_reg[1]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.884      ;
; 47.064 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[7]                                          ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.888      ;
; 47.075 ; sel_wave:u_sel_wave|da_out_reg[0]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.869      ;
; 47.077 ; sel_wave:u_sel_wave|da_out_reg[10]                                                                              ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.867      ;
; 47.084 ; sel_wave:u_sel_wave|da_out_reg[7]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.860      ;
; 47.086 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[12]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.865      ;
; 47.090 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[13]                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.228     ; 2.669      ;
; 47.091 ; sel_wave:u_sel_wave|da_out_reg[6]                                                                               ; amplitude_control:u_amplitude_control|data_out[10]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.860      ;
; 47.094 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[4]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.857      ;
; 47.101 ; sel_wave:u_sel_wave|da_out_reg[3]                                                                               ; amplitude_control:u_amplitude_control|data_out[7]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.850      ;
; 47.107 ; sel_wave:u_sel_wave|da_out_reg[5]                                                                               ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 2.837      ;
; 47.109 ; sel_wave:u_sel_wave|da_out_reg[2]                                                                               ; amplitude_control:u_amplitude_control|data_out[11]                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.842      ;
; 47.109 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                              ; amplitude_control:u_amplitude_control|data_out[6]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.842      ;
; 47.110 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                              ; amplitude_control:u_amplitude_control|data_out[8]                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.841      ;
; 47.115 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[16]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 2.827      ;
; 47.116 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[20]                                         ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.045     ; 2.826      ;
+--------+-----------------------------------------------------------------------------------------------------------------+-----------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                                          ;
+-------+-------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                                               ; To Node                                                                                                          ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.187 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                      ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; amplitude_selector:u_amplitude_selector|amp_sel[0]                      ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.307      ;
; 0.194 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                      ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.314      ;
; 0.201 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout   ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.321      ;
; 0.213 ; amplitude_selector:u_amplitude_selector|amp_sel[1]                      ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.333      ;
; 0.230 ; amplitude_selector:u_amplitude_selector|amp_sel[2]                      ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.350      ;
; 0.252 ; add_32bit:u_add_32bit|add[26]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.583      ;
; 0.262 ; add_32bit:u_add_32bit|add[30]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.593      ;
; 0.268 ; add_32bit:u_add_32bit|add[21]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.599      ;
; 0.270 ; add_32bit:u_add_32bit|add[22]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.601      ;
; 0.271 ; add_32bit:u_add_32bit|add[31]                                           ; add_32bit:u_add_32bit|add[31]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.391      ;
; 0.275 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[17] ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.396      ;
; 0.278 ; add_32bit:u_add_32bit|add[31]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.609      ;
; 0.282 ; add_32bit:u_add_32bit|add[20]                                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.613      ;
; 0.283 ; key_con:u_key_con|sel[1]                                                ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.404      ;
; 0.283 ; key_con:u_key_con|sel[1]                                                ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.404      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[5]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[5]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[3]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[3]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[1]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[1]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.293 ; add_32bit:u_add_32bit|add[15]                                           ; add_32bit:u_add_32bit|add[15]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; add_32bit:u_add_32bit|add[3]                                            ; add_32bit:u_add_32bit|add[3]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[5]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[5]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[3]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[3]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[1]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[1]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[6]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[6]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.294 ; add_32bit:u_add_32bit|add[19]                                           ; add_32bit:u_add_32bit|add[19]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[18]                                           ; add_32bit:u_add_32bit|add[18]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[17]                                           ; add_32bit:u_add_32bit|add[17]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[16]                                           ; add_32bit:u_add_32bit|add[16]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[12]                                           ; add_32bit:u_add_32bit|add[12]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[11]                                           ; add_32bit:u_add_32bit|add[11]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[9]                                            ; add_32bit:u_add_32bit|add[9]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[8]                                            ; add_32bit:u_add_32bit|add[8]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[7]                                            ; add_32bit:u_add_32bit|add[7]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[6]                                            ; add_32bit:u_add_32bit|add[6]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[5]                                            ; add_32bit:u_add_32bit|add[5]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[1]                                            ; add_32bit:u_add_32bit|add[1]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[6]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[6]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[4]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[4]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[2]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[2]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.295 ; add_32bit:u_add_32bit|add[14]                                           ; add_32bit:u_add_32bit|add[14]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[4]                                            ; add_32bit:u_add_32bit|add[4]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[2]                                            ; add_32bit:u_add_32bit|add[2]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                          ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[4]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[4]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[2]  ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[2]                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                          ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                          ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.296 ; add_32bit:u_add_32bit|add[10]                                           ; add_32bit:u_add_32bit|add[10]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.416      ;
; 0.299 ; add_32bit:u_add_32bit|add[0]                                            ; add_32bit:u_add_32bit|add[0]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.419      ;
; 0.302 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.423      ;
; 0.302 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[15] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[15]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.423      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.423      ;
; 0.303 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[15]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.423      ;
; 0.303 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[31] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[31]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[13] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[13]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                         ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.423      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                         ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.423      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[27]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[17]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                         ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                          ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                         ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[31] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[31]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[13] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kh[13]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[29] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[29]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[27] ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kl[27]                                          ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
+-------+-------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.620  ; 9.620        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.622  ; 9.622        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.378 ; 10.378       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.380 ; 10.380       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                 ;
+--------+--------------+----------------+-----------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type            ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+-----------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0   ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0   ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0    ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_control:u_amplitude_control|data_out[0]                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_control:u_amplitude_control|data_out[1]                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_control:u_amplitude_control|data_out[2]                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_control:u_amplitude_control|data_out[3]                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_control:u_amplitude_control|data_out[4]                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_control:u_amplitude_control|data_out[5]                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_control:u_amplitude_control|data_out[6]                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_control:u_amplitude_control|data_out[8]                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|amp_sel[0]                                                                ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|amp_sel[1]                                                                ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|amp_sel[2]                                                                ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; amplitude_selector:u_amplitude_selector|key_delay:u_amp_up_delay|kout                                             ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[16]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[18]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[20]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[22]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[23]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[24]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[25]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[26]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[28]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[10]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[11]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[12]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[13]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[14]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                    ;
+--------+--------------+----------------+-----------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 1.718 ; 1.976 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 1.718 ; 1.976 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 1.440 ; 1.742 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 1.544 ; 1.854 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 1.464 ; 1.805 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -1.046 ; -1.346 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.079 ; -1.392 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -1.061 ; -1.400 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -1.046 ; -1.346 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -1.017 ; -1.370 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                           ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 1.519 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 3.014 ; 3.141 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 2.223 ; 2.306 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 2.224 ; 2.307 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 2.243 ; 2.331 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 2.166 ; 2.241 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 2.216 ; 2.297 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 3.014 ; 3.141 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 1.906 ; 1.948 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 2.131 ; 2.218 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 1.961 ; 2.023 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 2.131 ; 2.219 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 2.154 ; 2.245 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 2.131 ; 2.218 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 2.148 ; 2.232 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 2.163 ; 2.091 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 1.584 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 1.653 ; 1.693 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 1.957 ; 2.037 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 1.958 ; 2.038 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 1.976 ; 2.061 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 1.902 ; 1.975 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 1.950 ; 2.029 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 2.754 ; 2.878 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 1.653 ; 1.693 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 1.870 ; 1.955 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 1.708 ; 1.768 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 1.870 ; 1.955 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 1.892 ; 1.980 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 1.870 ; 1.954 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 1.887 ; 1.968 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 1.899 ; 1.830 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Fast 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                                                                ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Clock                                                  ; Setup  ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Worst-case Slack                                       ; 40.528 ; 0.187 ; N/A      ; N/A     ; 1.616               ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 1.616               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 9.594               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 40.528 ; 0.187 ; N/A      ; N/A     ; 24.716              ;
; Design-wide TNS                                        ; 0.0    ; 0.0   ; 0.0      ; 0.0     ; 0.0                 ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000  ; 0.000 ; N/A      ; N/A     ; 0.000               ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 3.383 ; 3.577 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 3.383 ; 3.577 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 2.901 ; 3.102 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 2.972 ; 3.196 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 2.917 ; 3.224 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -1.046 ; -1.346 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.079 ; -1.392 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -1.061 ; -1.400 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -1.046 ; -1.346 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -1.017 ; -1.370 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                           ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 3.199 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 6.064 ; 6.110 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 4.926 ; 4.841 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 4.925 ; 4.844 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 4.970 ; 4.891 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 4.828 ; 4.733 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 4.917 ; 4.830 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 6.064 ; 6.110 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 4.191 ; 4.159 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 4.671 ; 4.637 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 4.286 ; 4.266 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 4.675 ; 4.636 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 4.725 ; 4.688 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 4.676 ; 4.637 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 4.726 ; 4.666 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 4.566 ; 4.624 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 3.157 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 1.653 ; 1.693 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 1.957 ; 2.037 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 1.958 ; 2.038 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 1.976 ; 2.061 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 1.902 ; 1.975 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 1.950 ; 2.029 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 2.754 ; 2.878 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 1.653 ; 1.693 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 1.870 ; 1.955 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 1.708 ; 1.768 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 1.870 ; 1.955 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 1.892 ; 1.980 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 1.870 ; 1.954 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 1.887 ; 1.968 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 1.899 ; 1.830 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                    ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin           ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; PD            ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_CLK       ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[4]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[5]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[6]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[7]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[8]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[9]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[10]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[11]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[12]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[13]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; SYS_RST                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; SYS_CLK                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[2]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[0]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[1]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD            ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD            ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD            ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_CLK       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; DAC_DATA[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[8]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[9]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[10]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[11]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[12]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[13]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Setup Transfers                                                                                                                                           ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 5969     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Hold Transfers                                                                                                                                            ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 5969     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths                            ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 2     ; 2    ;
; Unconstrained Input Ports       ; 4     ; 4    ;
; Unconstrained Input Port Paths  ; 289   ; 289  ;
; Unconstrained Output Ports      ; 15    ; 15   ;
; Unconstrained Output Port Paths ; 15    ; 15   ;
+---------------------------------+-------+------+


+------------------------------------+
; TimeQuest Timing Analyzer Messages ;
+------------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit TimeQuest Timing Analyzer
    Info: Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
    Info: Processing started: Thu Jul 31 20:41:12 2025
Info: Command: quartus_sta DAC904 -c DAC904
Info: qsta_default_script.tcl version: #1
Warning (20028): Parallel compilation is not licensed and has been disabled
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Info (332104): Reading SDC File: '../doc/SDC1.sdc'
Info (332110): Deriving PLL clocks
    Info (332110): create_generated_clock -source {u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]} {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]}
Info (332151): Clock uncertainty is not calculated until you update the timing netlist.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key2_delay|kout was determined to be a clock but was found without an associated clock assignment.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key1_delay|kout was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info: Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Info (332146): Worst-case setup slack is 40.528
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    40.528               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.453
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.453               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.934               0.000 CLK_50M 
    Info (332119):    24.718               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Warning (332060): Node: key_con:u_key_con|key_delay:u_key2_delay|kout was determined to be a clock but was found without an associated clock assignment.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key1_delay|kout was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 41.013
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    41.013               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.402
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.402               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.943               0.000 CLK_50M 
    Info (332119):    24.716               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Fast 1200mV 0C Model
Warning (332060): Node: key_con:u_key_con|key_delay:u_key2_delay|kout was determined to be a clock but was found without an associated clock assignment.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key1_delay|kout was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 45.958
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    45.958               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.187
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.187               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 2.060
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     2.060               0.000 CLK_165M 
    Info (332119):     9.594               0.000 CLK_50M 
    Info (332119):    24.734               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 7 warnings
    Info: Peak virtual memory: 4635 megabytes
    Info: Processing ended: Thu Jul 31 20:41:13 2025
    Info: Elapsed time: 00:00:01
    Info: Total CPU time (on all processors): 00:00:01


