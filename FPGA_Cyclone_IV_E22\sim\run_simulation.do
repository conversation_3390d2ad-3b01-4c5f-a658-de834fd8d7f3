# ModelSim仿真脚本
# 用于验证幅度控制LUT方案

# 创建工作库
vlib work

# 编译设计文件
echo "编译RTL文件..."
vlog -work work ../rtl/key_delay.v
vlog -work work ../rtl/amplitude_control.v
vlog -work work ../rtl/amplitude_selector.v

# 编译测试文件
echo "编译测试文件..."
vlog -work work tb_amplitude_control.v

# 仿真幅度控制模块
echo "开始幅度控制模块仿真..."
vsim -t ps work.tb_amplitude_control

# 添加信号到波形窗口
add wave -divider "时钟和复位"
add wave /tb_amplitude_control/clk
add wave /tb_amplitude_control/rst_n

add wave -divider "输入信号"
add wave -radix unsigned /tb_amplitude_control/data_in
add wave -radix unsigned /tb_amplitude_control/amp_sel

add wave -divider "输出信号"
add wave -radix unsigned /tb_amplitude_control/data_out
add wave -radix unsigned /tb_amplitude_control/expected_out

add wave -divider "内部信号"
add wave -radix signed /tb_amplitude_control/uut/signed_input
add wave -radix signed /tb_amplitude_control/uut/scaled_signed
add wave -radix unsigned /tb_amplitude_control/uut/final_output

# 运行仿真
run 10us

# 缩放波形显示
wave zoom full

echo "仿真完成！请查看波形和控制台输出。"
