module DAC904_TOP(
	input  wire 			 SYS_CLK	,
	input	 wire 			 SYS_RST	,
	input	 wire	[2:0]	 KEY_IN		,
	output wire				 PD				,
	output wire				 DAC_CLK	,
	output wire	[13:0] DAC_DATA
);


wire	CLK_20M;

wire 	[1 :0] sel			;
wire 	[31:0] fre_k		;
wire 	[11:0] addr			;
wire 	[13:0] wave_z		;
wire 	[13:0] wave_s		;
wire 	[13:0] wave_f		;
wire	[13:0] DATA_BUF	;
wire	[2:0]  amp_sel		;
wire	[7:0]  amp_percent	;
wire	[13:0] amp_data_out;


assign wave_f = addr[11] ? 14'b11_1111_1111_1111 : 14'b00_0000_0000_0000;


PLL_CLK u_PLL_CLK(
	.areset	(!SYS_RST	),
	.inclk0	(SYS_CLK	),
	.c0			(CLK_20M	)
);


ROM_Sin u_ROM_Sin(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_z		)
);


ROM_Tri ROM_Tri(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_s		)
);


add_32bit u_add_32bit(
	.clk	(CLK_20M	),
	.rst	(SYS_RST	),
	.fr_k (fre_k		),
	.adder(addr			)
);


key_con u_key_con(
	.clk			(CLK_20M	),
	.rst_n		(SYS_RST	),
	.key1_in	(KEY_IN[0]),
	.key2_in	(KEY_IN[1]),
	.key3_in  (KEY_IN[2]),
	.sel_wave	(sel			),
	.fre_k		(fre_k		)
);
sel_wave u_sel_wave(
	.clk		(CLK_20M	),
	.rst_n	(SYS_RST	),
	.sel		(sel			),
	.da_ina (wave_z		),
	.da_inb (wave_s		),
	.da_inc (wave_f		),
	.da_out (DATA_BUF	)
);

// 幅度选择控制模块 - 使用组合按键控制幅度
// 按键功能重新分配：
// KEY_IN[0]: 波形选择 (保持原功能)
// KEY_IN[1]: 频率调节 (保持原功能)
// KEY_IN[2]: 幅度调节 (新功能：单键循环切换幅度)
amplitude_selector u_amplitude_selector(
	.clk         (CLK_20M    ),
	.rst_n       (SYS_RST    ),
	.amp_up_key  (KEY_IN[2]  ),  // KEY_IN[2]: 幅度循环切换
	.amp_down_key(1'b1       ),  // 不使用减少键，只用循环切换
	.amp_sel     (amp_sel    ),
	.amp_percent (amp_percent)
);

// 精确幅度控制模块 - 替代原有的整数除法
amplitude_control u_amplitude_control(
	.clk      (CLK_20M     ),
	.rst_n    (SYS_RST     ),
	.data_in  (DATA_BUF    ),
	.amp_sel  (amp_sel     ),
	.data_out (amp_data_out)
);

assign PD = 1'b0;
assign DAC_CLK  = CLK_20M;
// 使用新的精确幅度控制输出，替代原有的整数除法计算
assign DAC_DATA = amp_data_out;

// 原有的整数除法方案 (已被替换，保留作为参考)
//localparam [7:0] SPI_VV = 8'd2;  // 除以2 = 50%幅度
//assign DAC_DATA = (DATA_BUF/SPI_VV) +(14'd8191 - 14'd8191/SPI_VV); // 50%幅度，修正直流偏置
//assign DAC_DATA = 14'd10000; // 固定输出测试

endmodule
