module DAC904_TOP(
	input  wire 			 SYS_CLK	,
	input	 wire 			 SYS_RST	,
	input	 wire	[2:0]	 KEY_IN		,
	output wire				 PD				,
	output wire				 DAC_CLK	,
	output wire	[13:0] DAC_DATA
);

//DAC904闂佹眹鍔岀€氼垳娆㈤銏犵闁惧繗顫夐弳鈺呮煕濡警鍎撴繛鏉戞楠炴垿濮€閻樻彃绲鹃悗娈垮枛妤犲繒妲6383 - DAC_DATA) * 闂佹寧鐭16383闂佹寧绋戦¨鈧紒杈ㄧ箓椤垽濡堕崨顓炵稻閻庢鍠栫换妯煎垝椤栨粍濯奸柕鍫濆閻熸繂霉閿濆牊纭堕柡浣靛€栧蹇涘箻閸愬弶鐦旈梺琛″亾闁诡垎浣锋捣缂傚倸鍊甸弲娑㈡儍椤掑嫭鏅€光偓閳ь剟鎮￠鐘冲磯妞ゆ牗宀稿顕€鏌

wire	CLK_20M;

wire 	[1 :0] sel			;
wire 	[31:0] fre_k		;
wire 	[11:0] addr			;
wire 	[13:0] wave_z		;
wire 	[13:0] wave_s		;
wire 	[13:0] wave_f		;
wire	[13:0] DATA_BUF	;

// 闂佸搫鍊介～澶屼焊濠靛鏋侀柣妤€鐗嗙粊
assign wave_f = addr[11] ? 14'b11_1111_1111_1111 : 14'b00_0000_0000_0000;

//闂備礁銇橀懗璺好规径鎰仢妞ゆ牗涓规笟鈧畷
PLL_CLK u_PLL_CLK(
	.areset	(!SYS_RST	),
	.inclk0	(SYS_CLK	),
	.c0			(CLK_20M	)
);

// 濠殿喗绻愮徊鑺ャ偊閳ユ枼鏋栭柕蹇婂墲濞堝爼鏌熺拠鈥虫珯缂佽鲸绱朞M闁
ROM_Sin u_ROM_Sin(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_z		)
);

// 婵炴垶鎸搁ˇ鎶斤綖濡も偓閳绘棃濮€閳╁啯顔嶉梺纭咁嚃閸ｎ垳妲愬鍖玀闁
ROM_Tri ROM_Tri(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_s		)
);

//闂侀潻闄勫妯侯焽閸愵亝顫曟い鏍ㄨ壘椤綁鏌
add_32bit u_add_32bit(
	.clk	(CLK_20M	),
	.rst	(SYS_RST	),
	.fr_k (fre_k		),
	.adder(addr			)
);

//闂佸湱顭堥ˇ鐢稿极椤撱垹绠崇憸宥夊春濡も偓铻ｉ柍銉ㄦ珪閸
key_con u_key_con(
	.clk			(CLK_20M	),
	.rst_n		(SYS_RST	),
	.key1_in	(KEY_IN[0]),
	.key2_in	(KEY_IN[1]),
	.key3_in  (KEY_IN[2]),
	.sel_wave	(sel			),
	.fre_k		(fre_k		)
);

//濠电偛顦崹褰掓嚋閻楀牊缍囬柟鎯у暱濮ｅ姊洪銏╂Ч閻庢哎鍔岃灒闁炽儴娅曢崑
sel_wave u_sel_wave(
	.clk		(CLK_20M	),
	.rst_n	(SYS_RST	),
	.sel		(sel			),
	.da_ina (wave_z		),
	.da_inb (wave_s		),
	.da_inc (wave_f		),
	.da_out (DATA_BUF	)
);

assign PD = 1'b0;
assign DAC_CLK  = CLK_20M;
localparam [7:0] SPI_VV = 8'd3;  // 除以2 = 50%幅度
assign DAC_DATA = (DATA_BUF/SPI_VV) +(14'd8191 - 14'd8191/SPI_VV); // 50%幅度，修正直流偏置 
//assign DAC_DATA = 14'd10000; // 闂佺儵鏅涢悺銊х矈閿曗偓闇夐悗锝庡幘濡叉悂鏌6V閻庡綊娼荤粻鎴ｃ亹

endmodule
