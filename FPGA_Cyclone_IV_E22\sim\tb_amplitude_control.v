`timescale 1ns / 1ps

/*
 * 幅度控制模块测试平台
 * 验证LUT方案的精度和功能正确性
 */

module tb_amplitude_control;

// 测试参数
parameter CLK_PERIOD = 50;  // 20MHz时钟周期 (50ns)
parameter SIM_TIME = 10000; // 仿真时间 (ns)

// 测试信号
reg         clk;
reg         rst_n;
reg  [13:0] data_in;
reg  [2:0]  amp_sel;
wire [13:0] data_out;

// 参考计算结果 (用于对比)
reg  [13:0] expected_out;
reg  [14:0] signed_in, scaled_signed;
integer     error_count;

// 实例化被测模块
amplitude_control uut (
    .clk      (clk),
    .rst_n    (rst_n),
    .data_in  (data_in),
    .amp_sel  (amp_sel),
    .data_out (data_out)
);

// 时钟生成
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

// 复位生成
initial begin
    rst_n = 0;
    #(CLK_PERIOD*2);
    rst_n = 1;
end

// 参考模型 (用于验证)
always @(*) begin
    signed_in = {1'b0, data_in} - 15'd8192;
    
    case (amp_sel)
        3'd0: scaled_signed = signed_in;                    // 100%
        3'd1: scaled_signed = signed_in - (signed_in >>> 3); // 87.5%
        3'd2: scaled_signed = (signed_in >>> 1) + (signed_in >>> 2); // 75%
        3'd3: scaled_signed = signed_in >>> 1;              // 50%
        3'd4: scaled_signed = (signed_in >>> 2) + (signed_in >>> 3); // 37.5%
        3'd5: scaled_signed = signed_in >>> 2;              // 25%
        3'd6: scaled_signed = signed_in >>> 3;              // 12.5%
        3'd7: scaled_signed = signed_in >>> 4;              // 6.25%
        default: scaled_signed = signed_in >>> 1;           // 默认50%
    endcase
    
    // 转换回无符号数
    if (scaled_signed[14]) 
        expected_out = 14'd0;  // 负溢出
    else if ((scaled_signed + 15'd8192) > 15'd16383)
        expected_out = 14'd16383;  // 正溢出
    else
        expected_out = scaled_signed[13:0] + 14'd8192;
end

// 测试序列
initial begin
    $display("========================================");
    $display("幅度控制模块测试开始");
    $display("========================================");
    
    // 初始化
    data_in = 14'd8192;  // 中心点
    amp_sel = 3'd3;      // 50%幅度
    error_count = 0;
    
    // 等待复位完成
    wait(rst_n);
    #(CLK_PERIOD*2);
    
    // 测试1: 中心点测试 (所有幅度级别)
    $display("\n--- 测试1: 中心点测试 ---");
    data_in = 14'd8192;
    for (integer i = 0; i < 8; i = i + 1) begin
        amp_sel = i;
        #(CLK_PERIOD*2);
        check_result($sformatf("中心点-幅度%0d", i));
    end
    
    // 测试2: 最大值测试
    $display("\n--- 测试2: 最大值测试 ---");
    data_in = 14'd16383;
    for (integer i = 0; i < 8; i = i + 1) begin
        amp_sel = i;
        #(CLK_PERIOD*2);
        check_result($sformatf("最大值-幅度%0d", i));
    end
    
    // 测试3: 最小值测试
    $display("\n--- 测试3: 最小值测试 ---");
    data_in = 14'd0;
    for (integer i = 0; i < 8; i = i + 1) begin
        amp_sel = i;
        #(CLK_PERIOD*2);
        check_result($sformatf("最小值-幅度%0d", i));
    end
    
    // 测试4: 典型正弦波值测试
    $display("\n--- 测试4: 典型正弦波值测试 ---");
    test_sine_values();
    
    // 测试5: 精度对比测试
    $display("\n--- 测试5: 精度对比测试 ---");
    precision_comparison_test();
    
    // 测试总结
    $display("\n========================================");
    if (error_count == 0) begin
        $display("✅ 所有测试通过！LUT方案工作正常");
    end else begin
        $display("❌ 发现 %0d 个错误", error_count);
    end
    $display("========================================");
    
    #(CLK_PERIOD*10);
    $finish;
end

// 结果检查任务
task check_result(input string test_name);
    begin
        #1; // 等待组合逻辑稳定
        if (data_out !== expected_out) begin
            $display("❌ %s: 输入=%0d, 幅度选择=%0d, 期望=%0d, 实际=%0d", 
                     test_name, data_in, amp_sel, expected_out, data_out);
            error_count = error_count + 1;
        end else begin
            $display("✅ %s: 输入=%0d, 输出=%0d", test_name, data_in, data_out);
        end
    end
endtask

// 正弦波值测试
task test_sine_values();
    reg [13:0] sine_values [0:7];
    integer i;
    begin
        // 典型正弦波采样点 (来自Sin_Wave.mif)
        sine_values[0] = 14'h2000;  // 0度
        sine_values[1] = 14'h2D41;  // 45度
        sine_values[2] = 14'h3FFF;  // 90度  
        sine_values[3] = 14'h2D41;  // 135度
        sine_values[4] = 14'h2000;  // 180度
        sine_values[5] = 14'h12BF;  // 225度
        sine_values[6] = 14'h0001;  // 270度
        sine_values[7] = 14'h12BF;  // 315度
        
        amp_sel = 3'd3; // 50%幅度
        for (i = 0; i < 8; i = i + 1) begin
            data_in = sine_values[i];
            #(CLK_PERIOD*2);
            check_result($sformatf("正弦波点%0d", i));
        end
    end
endtask

// 精度对比测试 (LUT vs 整数除法)
task precision_comparison_test();
    reg [13:0] old_method_result;
    integer precision_errors;
    begin
        precision_errors = 0;
        amp_sel = 3'd3; // 50%幅度
        
        $display("输入值    LUT结果   整数除法   精度差异");
        $display("--------------------------------------");
        
        for (integer val = 0; val <= 16383; val = val + 1000) begin
            data_in = val;
            #(CLK_PERIOD*2);
            
            // 原有整数除法方法
            old_method_result = (val/2) + (14'd8191 - 14'd8191/2);
            
            $display("%5d     %5d     %5d      %5d", 
                     val, data_out, old_method_result, 
                     (data_out > old_method_result) ? 
                     (data_out - old_method_result) : 
                     (old_method_result - data_out));
            
            // 统计精度改善
            if (data_out !== old_method_result) begin
                precision_errors = precision_errors + 1;
            end
        end
        
        $display("精度改善点数: %0d / 17", precision_errors);
    end
endtask

endmodule

/*
 * 系统级测试平台 - 测试完整的DAC904_TOP
 */
module tb_dac904_system;

parameter CLK_PERIOD = 20;  // 50MHz输入时钟

// 系统信号
reg         SYS_CLK;
reg         SYS_RST;
reg  [2:0]  KEY_IN;
wire        PD;
wire        DAC_CLK;
wire [13:0] DAC_DATA;

// 实例化顶层模块
DAC904_TOP uut_system (
    .SYS_CLK  (SYS_CLK),
    .SYS_RST  (SYS_RST),
    .KEY_IN   (KEY_IN),
    .PD       (PD),
    .DAC_CLK  (DAC_CLK),
    .DAC_DATA (DAC_DATA)
);

// 时钟生成
initial begin
    SYS_CLK = 0;
    forever #(CLK_PERIOD/2) SYS_CLK = ~SYS_CLK;
end

// 系统测试
initial begin
    $display("========================================");
    $display("DAC904系统级测试开始");
    $display("========================================");

    // 初始化
    SYS_RST = 1'b1;
    KEY_IN = 3'b111;  // 所有按键释放

    // 复位
    #(CLK_PERIOD*10);
    SYS_RST = 1'b0;
    #(CLK_PERIOD*5);
    SYS_RST = 1'b1;

    // 等待PLL稳定
    #(CLK_PERIOD*100);

    $display("系统复位完成，开始功能测试...");

    // 测试波形选择
    $display("\n--- 测试波形选择 ---");
    test_wave_selection();

    // 测试幅度控制
    $display("\n--- 测试幅度控制 ---");
    test_amplitude_control();

    $display("\n========================================");
    $display("系统级测试完成");
    $display("========================================");

    #(CLK_PERIOD*100);
    $finish;
end

// 波形选择测试
task test_wave_selection();
    begin
        // 按KEY_IN[0]切换波形
        repeat(4) begin
            #(CLK_PERIOD*1000);  // 观察当前波形
            KEY_IN[0] = 1'b0;    // 按下按键
            #(CLK_PERIOD*10);
            KEY_IN[0] = 1'b1;    // 释放按键
            $display("波形切换，当前DAC输出: %d", DAC_DATA);
        end
    end
endtask

// 幅度控制测试
task test_amplitude_control();
    begin
        // 按KEY_IN[2]切换幅度
        repeat(6) begin
            #(CLK_PERIOD*1000);  // 观察当前幅度
            KEY_IN[2] = 1'b0;    // 按下按键
            #(CLK_PERIOD*10);
            KEY_IN[2] = 1'b1;    // 释放按键
            $display("幅度切换，当前DAC输出: %d", DAC_DATA);
        end
    end
endtask

endmodule
