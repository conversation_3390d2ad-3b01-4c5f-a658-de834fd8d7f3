#!/usr/bin/env python3
"""
DAC904 幅度缩放LUT生成器
生成精确的幅度缩放查找表，替代整数除法运算
"""

import math
import os

def generate_amplitude_lut():
    """
    生成幅度缩放LUT
    输入：14位波形数据 (0-16383)
    输出：缩放后的14位数据，支持多种幅度级别
    """
    
    # LUT参数配置
    INPUT_WIDTH = 14        # 输入数据位宽
    OUTPUT_WIDTH = 14       # 输出数据位宽
    LUT_DEPTH = 2**INPUT_WIDTH  # LUT深度：16384
    
    # 支持的幅度级别 (以百分比表示)
    amplitude_levels = [
        100,  # 100% - 全幅度
        75,   # 75%  - 3/4幅度  
        50,   # 50%  - 1/2幅度
        25,   # 25%  - 1/4幅度
        12.5, # 12.5%- 1/8幅度
    ]
    
    # DAC中心点 (理论值8191.5，实际使用8192)
    DAC_CENTER = 8192
    DAC_MAX = 16383
    
    print("正在生成幅度缩放LUT...")
    print(f"输入范围: 0 - {DAC_MAX}")
    print(f"LUT深度: {LUT_DEPTH}")
    print(f"支持幅度级别: {amplitude_levels}")
    
    # 为每个幅度级别生成LUT
    for i, amplitude in enumerate(amplitude_levels):
        amplitude_factor = amplitude / 100.0
        
        # 生成MIF文件
        mif_filename = f"../ip_core/ROM/Amplitude_LUT_{int(amplitude*10):03d}.mif"
        
        with open(mif_filename, 'w') as f:
            # MIF文件头
            f.write(f"-- 幅度缩放LUT: {amplitude}%\n")
            f.write(f"-- 生成时间: {__import__('datetime').datetime.now()}\n")
            f.write(f"-- 输入: 14位波形数据 (0-{DAC_MAX})\n")
            f.write(f"-- 输出: 14位缩放数据，{amplitude}%幅度\n\n")
            
            f.write(f"DEPTH = {LUT_DEPTH};\n")
            f.write(f"WIDTH = {OUTPUT_WIDTH};\n")
            f.write("ADDRESS_RADIX = HEX;\n")
            f.write("DATA_RADIX = HEX;\n")
            f.write("CONTENT\n")
            f.write("BEGIN\n")
            
            # 生成LUT数据
            for addr in range(LUT_DEPTH):
                # 将输入数据转换为有符号数 (以DAC_CENTER为中心)
                signed_input = addr - DAC_CENTER
                
                # 应用幅度缩放
                scaled_signed = signed_input * amplitude_factor
                
                # 转换回无符号数并添加直流偏置
                scaled_output = int(scaled_signed + DAC_CENTER)
                
                # 限制输出范围
                scaled_output = max(0, min(DAC_MAX, scaled_output))
                
                # 写入MIF格式
                f.write(f"{addr:04X} : {scaled_output:04X};\n")
            
            f.write("END;\n")
        
        print(f"生成完成: {mif_filename} (幅度: {amplitude}%)")
    
    # 生成幅度控制参数头文件
    generate_amplitude_params(amplitude_levels)
    
    print("\n✅ 所有LUT文件生成完成！")

def generate_amplitude_params(amplitude_levels):
    """生成Verilog参数文件"""
    
    param_file = "../rtl/amplitude_params.vh"
    
    with open(param_file, 'w') as f:
        f.write("// 幅度缩放LUT参数定义\n")
        f.write("// 自动生成，请勿手动修改\n\n")
        
        f.write("// 幅度级别定义\n")
        for i, amplitude in enumerate(amplitude_levels):
            f.write(f"localparam [2:0] AMP_LEVEL_{int(amplitude*10):03d} = 3'd{i};\n")
        
        f.write(f"\n// 支持的幅度级别数量\n")
        f.write(f"localparam [2:0] AMP_LEVELS_COUNT = 3'd{len(amplitude_levels)};\n")
        
        f.write(f"\n// LUT深度和位宽\n")
        f.write(f"localparam [15:0] AMP_LUT_DEPTH = 16'd16384;\n")
        f.write(f"localparam [3:0]  AMP_LUT_WIDTH = 4'd14;\n")
    
    print(f"参数文件生成: {param_file}")

def generate_test_vectors():
    """生成测试向量用于验证LUT精度"""
    
    test_file = "../sim/amplitude_test_vectors.txt"
    os.makedirs("../sim", exist_ok=True)
    
    with open(test_file, 'w') as f:
        f.write("# 幅度缩放LUT测试向量\n")
        f.write("# 格式: 输入值 期望输出(100%) 期望输出(50%) 期望输出(25%)\n")
        
        test_values = [0, 4096, 8192, 12288, 16383]  # 关键测试点
        
        for val in test_values:
            signed_val = val - 8192
            
            out_100 = val  # 100%不变
            out_50 = int(signed_val * 0.5 + 8192)  # 50%缩放
            out_25 = int(signed_val * 0.25 + 8192) # 25%缩放
            
            f.write(f"{val:5d} {out_100:5d} {out_50:5d} {out_25:5d}\n")
    
    print(f"测试向量生成: {test_file}")

if __name__ == "__main__":
    # 确保输出目录存在
    os.makedirs("../ip_core/ROM", exist_ok=True)
    os.makedirs("../rtl", exist_ok=True)
    
    # 生成LUT
    generate_amplitude_lut()
    
    # 生成测试向量
    generate_test_vectors()
    
    print("\n🎯 使用说明:")
    print("1. 将生成的MIF文件导入Quartus IP Catalog")
    print("2. 创建ROM IP核，选择对应的MIF文件")
    print("3. 在顶层模块中实例化幅度控制模块")
    print("4. 使用3位控制信号选择幅度级别")
