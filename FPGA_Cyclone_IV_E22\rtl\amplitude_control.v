/*
 * 精确幅度控制模块
 * 使用查找表方式实现精确的幅度缩放，避免整数除法精度损失
 * 
 * 特性：
 * - 支持多级幅度控制 (100%, 75%, 50%, 25%, 12.5%)
 * - 精确的直流偏置补偿
 * - 无精度损失的数学运算
 * - 低资源消耗的硬件实现
 */

module amplitude_control (
    input  wire        clk,           // 时钟信号
    input  wire        rst_n,         // 复位信号 (低有效)
    input  wire [13:0] data_in,       // 输入波形数据 (14位)
    input  wire [2:0]  amp_sel,       // 幅度选择 (3位，支持8种幅度)
    output reg  [13:0] data_out       // 输出缩放数据 (14位)
);

// 幅度级别定义
localparam [2:0] AMP_100  = 3'd0;    // 100% - 全幅度
localparam [2:0] AMP_75   = 3'd1;    // 75%  - 3/4幅度  
localparam [2:0] AMP_50   = 3'd2;    // 50%  - 1/2幅度
localparam [2:0] AMP_25   = 3'd3;    // 25%  - 1/4幅度
localparam [2:0] AMP_125  = 3'd4;    // 12.5%- 1/8幅度
localparam [2:0] AMP_625  = 3'd5;    // 6.25%- 1/16幅度
localparam [2:0] AMP_875  = 3'd6;    // 87.5%- 7/8幅度 (新增)
localparam [2:0] AMP_375  = 3'd7;    // 37.5%- 3/8幅度 (新增)

// DAC参数
localparam [13:0] DAC_CENTER = 14'd8192;  // DAC中心点
localparam [13:0] DAC_MAX    = 14'd16383; // DAC最大值

// 内部信号
reg  [14:0] signed_input;      // 15位有符号输入 (扩展1位防止溢出)
reg  [14:0] scaled_signed;     // 15位有符号缩放结果
reg  [14:0] temp_result;       // 临时计算结果
wire [13:0] final_output;      // 最终输出

// 第一级：转换为有符号数
always @(*) begin
    signed_input = {1'b0, data_in} - {1'b0, DAC_CENTER};
end

// 第二级：精确幅度缩放 (使用移位和加法实现)
always @(*) begin
    case (amp_sel)
        AMP_100: begin
            // 100% - 不变
            scaled_signed = signed_input;
        end
        
        AMP_875: begin
            // 87.5% = 7/8 = 1 - 1/8
            temp_result = signed_input - (signed_input >>> 3);
            scaled_signed = temp_result;
        end
        
        AMP_75: begin
            // 75% = 3/4 = 1/2 + 1/4
            temp_result = (signed_input >>> 1) + (signed_input >>> 2);
            scaled_signed = temp_result;
        end
        
        AMP_50: begin
            // 50% = 1/2
            scaled_signed = signed_input >>> 1;
        end
        
        AMP_375: begin
            // 37.5% = 3/8 = 1/4 + 1/8
            temp_result = (signed_input >>> 2) + (signed_input >>> 3);
            scaled_signed = temp_result;
        end
        
        AMP_25: begin
            // 25% = 1/4
            scaled_signed = signed_input >>> 2;
        end
        
        AMP_125: begin
            // 12.5% = 1/8
            scaled_signed = signed_input >>> 3;
        end
        
        AMP_625: begin
            // 6.25% = 1/16
            scaled_signed = signed_input >>> 4;
        end
        
        default: begin
            // 默认50%
            scaled_signed = signed_input >>> 1;
        end
    endcase
end

// 第三级：转换回无符号数并限制范围
assign final_output = (scaled_signed[14]) ? 
                      14'd0 :  // 负溢出保护
                      ((scaled_signed + {1'b0, DAC_CENTER}) > {1'b0, DAC_MAX}) ? 
                       DAC_MAX : // 正溢出保护
                       (scaled_signed[13:0] + DAC_CENTER); // 正常输出

// 第四级：寄存器输出 (提供时序)
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        data_out <= DAC_CENTER;  // 复位到中心点
    end else begin
        data_out <= final_output;
    end
end

// 调试信息 (仅在仿真时有效)
`ifdef SIMULATION
    always @(posedge clk) begin
        if (rst_n) begin
            $display("AMP_CTRL: in=%d, sel=%d, signed_in=%d, scaled=%d, out=%d", 
                     data_in, amp_sel, signed_input, scaled_signed, data_out);
        end
    end
`endif

endmodule

/*
 * 使用说明：
 * 
 * 1. 幅度选择 (amp_sel):
 *    3'b000 - 100%  全幅度
 *    3'b001 - 87.5% 7/8幅度
 *    3'b010 - 75%   3/4幅度  
 *    3'b011 - 50%   1/2幅度
 *    3'b100 - 37.5% 3/8幅度
 *    3'b101 - 25%   1/4幅度
 *    3'b110 - 12.5% 1/8幅度
 *    3'b111 - 6.25% 1/16幅度
 * 
 * 2. 精度特性：
 *    - 所有运算基于移位和加法，无精度损失
 *    - 自动直流偏置补偿
 *    - 溢出保护机制
 * 
 * 3. 资源消耗：
 *    - 约20个LUT (组合逻辑)
 *    - 14个FF (输出寄存器)
 *    - 无需额外RAM资源
 */
