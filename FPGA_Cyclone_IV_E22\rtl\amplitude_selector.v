/*
 * 幅度选择控制模块
 * 通过按键控制幅度级别，提供用户友好的幅度调节接口
 */

module amplitude_selector (
    input  wire       clk,           // 时钟信号
    input  wire       rst_n,         // 复位信号 (低有效)
    input  wire       amp_up_key,    // 幅度增加按键
    input  wire       amp_down_key,  // 幅度减少按键
    output reg  [2:0] amp_sel,       // 幅度选择输出
    output reg  [7:0] amp_percent    // 当前幅度百分比 (用于显示)
);

// 幅度级别定义 (与amplitude_control.v保持一致)
localparam [2:0] AMP_100  = 3'd0;    // 100%
localparam [2:0] AMP_875  = 3'd1;    // 87.5%
localparam [2:0] AMP_75   = 3'd2;    // 75%
localparam [2:0] AMP_50   = 3'd3;    // 50%
localparam [2:0] AMP_375  = 3'd4;    // 37.5%
localparam [2:0] AMP_25   = 3'd5;    // 25%
localparam [2:0] AMP_125  = 3'd6;    // 12.5%
localparam [2:0] AMP_625  = 3'd7;    // 6.25%

// 按键防抖信号
wire amp_up_pulse;
wire amp_down_pulse;

// 按键防抖模块实例化
key_delay u_amp_up_delay (
    .clk     (clk),
    .kin     (amp_up_key),
    .key_out (amp_up_pulse)
);

key_delay u_amp_down_delay (
    .clk     (clk),
    .kin     (amp_down_key),
    .key_out (amp_down_pulse)
);

// 幅度级别控制 - 支持单键循环切换
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        amp_sel <= AMP_50;  // 默认50%幅度
    end else begin
        // 单键循环切换幅度 (按常用顺序排列)
        if (amp_up_pulse) begin
            case (amp_sel)
                AMP_25:  amp_sel <= AMP_50;   // 25% -> 50%
                AMP_50:  amp_sel <= AMP_75;   // 50% -> 75%
                AMP_75:  amp_sel <= AMP_100;  // 75% -> 100%
                AMP_100: amp_sel <= AMP_125;  // 100% -> 12.5%
                AMP_125: amp_sel <= AMP_25;   // 12.5% -> 25% (循环)
                default: amp_sel <= AMP_50;   // 其他情况回到50%
            endcase
        end
        // 如果需要双键控制，保留减少功能
        else if (amp_down_pulse && !amp_up_pulse) begin
            case (amp_sel)
                AMP_100: amp_sel <= AMP_75;   // 100% -> 75%
                AMP_75:  amp_sel <= AMP_50;   // 75% -> 50%
                AMP_50:  amp_sel <= AMP_25;   // 50% -> 25%
                AMP_25:  amp_sel <= AMP_125;  // 25% -> 12.5%
                AMP_125: amp_sel <= AMP_100;  // 12.5% -> 100% (循环)
                default: amp_sel <= AMP_50;   // 其他情况回到50%
            endcase
        end
    end
end

// 幅度百分比显示 (用于调试或外部显示)
always @(*) begin
    case (amp_sel)
        AMP_100:  amp_percent = 8'd100;  // 100%
        AMP_875:  amp_percent = 8'd87;   // 87.5% (显示为87)
        AMP_75:   amp_percent = 8'd75;   // 75%
        AMP_50:   amp_percent = 8'd50;   // 50%
        AMP_375:  amp_percent = 8'd37;   // 37.5% (显示为37)
        AMP_25:   amp_percent = 8'd25;   // 25%
        AMP_125:  amp_percent = 8'd12;   // 12.5% (显示为12)
        AMP_625:  amp_percent = 8'd6;    // 6.25% (显示为6)
        default:  amp_percent = 8'd50;   // 默认50%
    endcase
end

// 调试信息
`ifdef SIMULATION
    always @(posedge clk) begin
        if (rst_n && (amp_up_pulse || amp_down_pulse)) begin
            $display("AMP_SEL: sel=%d, percent=%d%%", amp_sel, amp_percent);
        end
    end
`endif

endmodule

/*
 * 使用说明：
 * 
 * 1. 按键功能：
 *    - amp_up_key:   增加幅度 (6.25% -> 12.5% -> 25% -> ... -> 100%)
 *    - amp_down_key: 减少幅度 (100% -> 87.5% -> 75% -> ... -> 6.25%)
 * 
 * 2. 输出信号：
 *    - amp_sel:     3位幅度选择信号，连接到amplitude_control模块
 *    - amp_percent: 8位百分比值，可用于外部显示或调试
 * 
 * 3. 集成方式：
 *    - 将此模块的amp_sel输出连接到amplitude_control的amp_sel输入
 *    - 按键信号可以复用现有的KEY_IN信号
 */
